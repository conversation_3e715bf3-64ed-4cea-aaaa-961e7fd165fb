{"logs": [{"outputFile": "expo.modules.devclient.test.expo-dev-client-mergeDebugAndroidTestResources-35:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "38,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3721,3831,3938,4058", "endColumns": "109,106,119,107", "endOffsets": "3826,3933,4053,4161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\804afae0b0d5554ac4e7feda2f2ca6c6\\transformed\\react-android-0.79.5-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,289,363,451,523,590,666,745,833,919,991,1072,1157,1233,1315,1398,1475,1548,1621,1706,1780,1860,1930", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,210,284,358,446,518,585,661,740,828,914,986,1067,1152,1228,1310,1393,1470,1543,1616,1701,1775,1855,1925,2010"}, "to": {"startLines": "29,37,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2834,3635,4166,4240,4314,4402,4474,4541,4617,4696,4784,4870,4942,5105,5190,5266,5348,5431,5508,5581,5654,5840,5914,5994,6064", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "2903,3716,4235,4309,4397,4469,4536,4612,4691,4779,4865,4937,5018,5185,5261,5343,5426,5503,5576,5649,5734,5909,5989,6059,6144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "30,31,32,33,34,35,36,62", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2908,3008,3110,3211,3312,3417,3522,5739", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3003,3105,3206,3307,3412,3517,3630,5835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,5023", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,5100"}}]}]}