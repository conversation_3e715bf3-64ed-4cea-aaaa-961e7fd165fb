{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/utils/supabase/middleware.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\r\nimport { NextResponse, type NextRequest } from \"next/server\";\r\n\r\nexport async function updateSession(request: NextRequest) {\r\n  let supabaseResponse = NextResponse.next({\r\n    request,\r\n  });\r\n\r\n  const supabase = createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return request.cookies.getAll();\r\n        },\r\n        setAll(cookiesToSet) {\r\n          cookiesToSet.forEach(({ name, value }) =>\r\n            request.cookies.set(name, value)\r\n          );\r\n          supabaseResponse = NextResponse.next({\r\n            request,\r\n          });\r\n          cookiesToSet.forEach(({ name, value, options }) =>\r\n            supabaseResponse.cookies.set(name, value, options)\r\n          );\r\n        },\r\n      },\r\n    }\r\n  );\r\n\r\n  // Do not run code between createServerClient and\r\n  // supabase.auth.getUser(). A simple mistake could make it very hard to debug\r\n  // issues with users being randomly logged out.\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  const { pathname } = request.nextUrl;\r\n\r\n  // Define protected path prefixes\r\n  const protectedPrefixes = [\r\n    \"/dashboard\",\r\n    \"/onboarding\",\r\n    \"/choose-role\",\r\n    // Add other protected prefixes if needed\r\n  ];\r\n\r\n  // Check if the current path is a protected path\r\n  const isProtectedRoute = protectedPrefixes.some((prefix) => pathname.startsWith(prefix));\r\n\r\n  // Redirect to login if user is not authenticated AND accessing a protected path\r\n  if (!user && isProtectedRoute) {\r\n    const url = request.nextUrl.clone();\r\n    url.pathname = \"/login\";\r\n    url.searchParams.set(\"next\", pathname); // Optionally pass the intended path\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // If user is authenticated, perform profile checks and role-based redirects\r\n  if (user) {\r\n    // Check customer profile and business profile\r\n    const [customerProfileRes, businessProfileRes] = await Promise.all([\r\n      supabase\r\n        .from(\"customer_profiles\")\r\n        .select(\"id\")\r\n        .eq(\"id\", user.id)\r\n        .maybeSingle(),\r\n      supabase\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_slug, trial_end_date, has_active_subscription\")\r\n        .eq(\"id\", user.id)\r\n        .maybeSingle(),\r\n    ]);\r\n\r\n    const customerProfile = customerProfileRes.data;\r\n    const customerError = customerProfileRes.error;\r\n    const businessProfile = businessProfileRes.data;\r\n    const businessError = businessProfileRes.error;\r\n\r\n    // Check if the user just logged out\r\n    const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';\r\n\r\n    if (customerError || businessError) {\r\n      // Allow request to proceed - profile fetch errors are not critical for middleware\r\n    } else if (!customerProfile && !businessProfile) {\r\n      // No profile found in either table - first time user\r\n      // Allow access ONLY to the choose-role page OR the onboarding page (onboarding is technically for business only now)\r\n      // EXCEPTION: If user just logged out, allow them to reach login page\r\n      if (pathname !== \"/choose-role\" && pathname !== \"/onboarding\" && !justLoggedOut) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/choose-role\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n    } else {\r\n      // Profile found - determine user type\r\n      const userType = customerProfile ? \"customer\" : \"business\";\r\n\r\n      // If business user hasn't completed onboarding (no slug), redirect to onboarding\r\n      // EXCEPTION: If user just logged out, allow them to reach login page\r\n      if (userType === \"business\" && !businessProfile?.business_slug && pathname !== \"/onboarding\" && !justLoggedOut) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/onboarding\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // --- CENTRALIZED SUBSCRIPTION STATUS CHECK ---\r\n      // CRITICAL: Since we now support free plans, access is no longer restricted anywhere in the application.\r\n      // This check is only for monitoring subscription consistency and feature-level restrictions.\r\n      if (userType === \"business\" && businessProfile?.business_slug) {\r\n        // Note: Subscription state validation could be implemented here for monitoring\r\n        // Feature-level restrictions are handled at the component level based on plan_id\r\n        // TODO: Consider implementing validation logic for critical inconsistencies\r\n      }\r\n      // --- End Centralized Subscription Status Check ---\r\n\r\n      // --- Free Tier Feature Access Checks ---\r\n      // Get the plan_id from the payment_subscriptions table\r\n      const { data: subscriptionData } = await supabase\r\n        .from(\"payment_subscriptions\")\r\n        .select(\"plan_id\")\r\n        .eq(\"business_profile_id\", user.id)\r\n        .order(\"created_at\", { ascending: false })\r\n        .limit(1)\r\n        .maybeSingle();\r\n\r\n      const planId = subscriptionData?.plan_id || \"free\";\r\n\r\n      if (userType === \"business\") {\r\n        // Free plan restrictions\r\n        if (planId === \"free\") {\r\n          // Check for analytics access\r\n          if (pathname.startsWith(\"/dashboard/business/analytics\")) {\r\n            // Redirect free tier users away from analytics pages\r\n            const url = request.nextUrl.clone();\r\n            url.pathname = \"/dashboard/business/plan\";\r\n            url.searchParams.set(\"upgrade\", \"analytics\");\r\n            return NextResponse.redirect(url);\r\n          }\r\n        }\r\n\r\n        // Both free and basic plan restrictions\r\n        if (planId === \"free\" || planId === \"basic\") {\r\n          // No additional restrictions for free and basic plans currently\r\n        }\r\n      }\r\n      // --- End Free Tier Feature Access Checks ---\r\n\r\n      // Redirect away from public auth pages if logged in and profile exists,\r\n      // UNLESS they just logged out and are heading to the login page.\r\n      if (\r\n        (pathname === \"/login\" ||\r\n          pathname === \"/choose-role\" ||\r\n          pathname === \"/choose-role\") &&\r\n        !(pathname === \"/login\" && justLoggedOut) // Don't redirect if just logged out and going to login\r\n        // We no longer redirect from \"/\" if logged in\r\n      ) {\r\n        const redirectPath =\r\n          userType === \"business\"\r\n            ? \"/dashboard/business\"\r\n            : \"/dashboard/customer\";\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = redirectPath;\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // Redirect away from onboarding if user is a customer\r\n      // EXCEPTION: If user just logged out, allow them to reach login page\r\n      if (pathname === \"/onboarding\" && userType === \"customer\" && !justLoggedOut) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/dashboard/customer\";\r\n        return NextResponse.redirect(url);\r\n      }\r\n\r\n      // Protect dashboard routes based on user type\r\n      // EXCEPTION: If user just logged out, allow them to reach login page\r\n      if (\r\n        pathname.startsWith(\"/dashboard/customer\") &&\r\n        userType !== \"customer\" &&\r\n        !justLoggedOut\r\n      ) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/dashboard/business\"; // Redirect to their correct dashboard\r\n        return NextResponse.redirect(url);\r\n      }\r\n      if (\r\n        pathname.startsWith(\"/dashboard/business\") &&\r\n        userType !== \"business\" &&\r\n        !justLoggedOut\r\n      ) {\r\n        const url = request.nextUrl.clone();\r\n        url.pathname = \"/dashboard/customer\"; // Redirect to their correct dashboard\r\n        return NextResponse.redirect(url);\r\n      }\r\n    }\r\n  } else {\r\n    // User is not logged in, but accessing a public path\r\n  }\r\n\r\n  // --- Original commented out logic ---\r\n  /*\r\n\r\n  */\r\n  // --- End of original commented out logic ---\r\n\r\n  // IMPORTANT: You *must* return the supabaseResponse object as it is.\r\n  // If you're creating a new response object with NextResponse.next() make sure to:\r\n  // 1. Pass the request in it, like so:\r\n  //    const myNewResponse = NextResponse.next({ request })\r\n  // 2. Copy over the cookies, like so:\r\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\r\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\r\n  //    the cookies!\r\n  // 4. Finally:\r\n  //    return myNewResponse\r\n  // If this is not done, you may be causing the browser and server to go out\r\n  // of sync and terminate the user's session prematurely!\r\n\r\n  return supabaseResponse;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,cAAc,OAAoB;IACtD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAE5B,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,iDAAiD;IACjD,6EAA6E;IAC7E,+CAA+C;IAE/C,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iCAAiC;IACjC,MAAM,oBAAoB;QACxB;QACA;QACA;KAED;IAED,gDAAgD;IAChD,MAAM,mBAAmB,kBAAkB,IAAI,CAAC,CAAC,SAAW,SAAS,UAAU,CAAC;IAEhF,gFAAgF;IAChF,IAAI,CAAC,QAAQ,kBAAkB;QAC7B,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG;QACf,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,WAAW,oCAAoC;QAC5E,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,4EAA4E;IAC5E,IAAI,MAAM;QACR,8CAA8C;QAC9C,MAAM,CAAC,oBAAoB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACjE,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;YACd,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,8DACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;SACf;QAED,MAAM,kBAAkB,mBAAmB,IAAI;QAC/C,MAAM,gBAAgB,mBAAmB,KAAK;QAC9C,MAAM,kBAAkB,mBAAmB,IAAI;QAC/C,MAAM,gBAAgB,mBAAmB,KAAK;QAE9C,oCAAoC;QACpC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB;QAEzE,IAAI,iBAAiB,eAAe;QAClC,kFAAkF;QACpF,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;YAC/C,qDAAqD;YACrD,qHAAqH;YACrH,qEAAqE;YACrE,IAAI,aAAa,kBAAkB,aAAa,iBAAiB,CAAC,eAAe;gBAC/E,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF,OAAO;YACL,sCAAsC;YACtC,MAAM,WAAW,kBAAkB,aAAa;YAEhD,iFAAiF;YACjF,qEAAqE;YACrE,IAAI,aAAa,cAAc,CAAC,iBAAiB,iBAAiB,aAAa,iBAAiB,CAAC,eAAe;gBAC9G,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,gDAAgD;YAChD,yGAAyG;YACzG,6FAA6F;YAC7F,IAAI,aAAa,cAAc,iBAAiB,eAAe;YAC7D,+EAA+E;YAC/E,iFAAiF;YACjF,4EAA4E;YAC9E;YACA,oDAAoD;YAEpD,0CAA0C;YAC1C,uDAAuD;YACvD,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,SACtC,IAAI,CAAC,yBACL,MAAM,CAAC,WACP,EAAE,CAAC,uBAAuB,KAAK,EAAE,EACjC,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC,GACN,WAAW;YAEd,MAAM,SAAS,kBAAkB,WAAW;YAE5C,IAAI,aAAa,YAAY;gBAC3B,yBAAyB;gBACzB,IAAI,WAAW,QAAQ;oBACrB,6BAA6B;oBAC7B,IAAI,SAAS,UAAU,CAAC,kCAAkC;wBACxD,qDAAqD;wBACrD,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;wBACjC,IAAI,QAAQ,GAAG;wBACf,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW;wBAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;oBAC/B;gBACF;gBAEA,wCAAwC;gBACxC,IAAI,WAAW,UAAU,WAAW,SAAS;gBAC3C,gEAAgE;gBAClE;YACF;YACA,8CAA8C;YAE9C,wEAAwE;YACxE,iEAAiE;YACjE,IACE,CAAC,aAAa,YACZ,aAAa,kBACb,aAAa,cAAc,KAC7B,CAAC,CAAC,aAAa,YAAY,aAAa,EAAE,uDAAuD;cAEjG;gBACA,MAAM,eACJ,aAAa,aACT,wBACA;gBACN,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,sDAAsD;YACtD,qEAAqE;YACrE,IAAI,aAAa,iBAAiB,aAAa,cAAc,CAAC,eAAe;gBAC3E,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG;gBACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YAEA,8CAA8C;YAC9C,qEAAqE;YACrE,IACE,SAAS,UAAU,CAAC,0BACpB,aAAa,cACb,CAAC,eACD;gBACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG,uBAAuB,sCAAsC;gBAC5E,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;YACA,IACE,SAAS,UAAU,CAAC,0BACpB,aAAa,cACb,CAAC,eACD;gBACA,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;gBACjC,IAAI,QAAQ,GAAG,uBAAuB,sCAAsC;gBAC5E,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF,OAAO;IACL,qDAAqD;IACvD;IAEA,uCAAuC;IACvC;;EAEA,GACA,8CAA8C;IAE9C,qEAAqE;IACrE,kFAAkF;IAClF,sCAAsC;IACtC,0DAA0D;IAC1D,qCAAqC;IACrC,qEAAqE;IACrE,2EAA2E;IAC3E,kBAAkB;IAClB,cAAc;IACd,0BAA0B;IAC1B,2EAA2E;IAC3E,wDAAwD;IAExD,OAAO;AACT"}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from \"next/server\";\r\nimport { updateSession } from \"./utils/supabase/middleware\";\r\nimport { Ratelimit } from \"@upstash/ratelimit\";\r\nimport { Redis } from \"@upstash/redis\";\r\n\r\n// Initialize Redis client for rate limiting only\r\nconst redisUrl = process.env.UPSTASH_REDIS_REST_URL;\r\nconst redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;\r\n\r\nif (!redisUrl || !redisToken) {\r\n  console.error(\"Upstash Redis URL or Token is not defined in environment variables.\");\r\n}\r\n\r\nconst redis = redisUrl && redisToken ? new Redis({ url: redisUrl, token: redisToken }) : null;\r\n\r\n// Initialize Rate Limiter\r\nconst maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || \"10\");\r\nconst windowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || \"10\");\r\n\r\nconst ratelimit = redis\r\n  ? new Ratelimit({\r\n      redis: redis,\r\n      limiter: Ratelimit.slidingWindow(maxRequests, `${windowSeconds} s`),\r\n      analytics: true,\r\n      prefix: \"@upstash/ratelimit/dukancard\",\r\n    })\r\n  : null;\r\n\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  // --- Domain and HTTPS Redirect Logic START ---\r\n  const url = request.nextUrl.clone();\r\n  const hostname = url.hostname;\r\n  const protocol = url.protocol;\r\n\r\n  // Only apply redirects in production environment and exclude development/testing domains\r\n  const isDevelopmentDomain = hostname.includes('localhost') ||\r\n                              hostname.includes('ngrok.io') ||\r\n                              hostname.includes('ngrok-free.app') ||\r\n                              hostname.includes('127.0.0.1');\r\n\r\n  if (process.env.NODE_ENV === 'production' && !isDevelopmentDomain) {\r\n    let shouldRedirect = false;\r\n\r\n    // Check for www redirect (www.dukancard.in -> dukancard.in)\r\n    if (hostname.startsWith('www.')) {\r\n      url.hostname = hostname.replace('www.', '');\r\n      shouldRedirect = true;\r\n    }\r\n\r\n    // Check for HTTPS redirect (http:// -> https://)\r\n    if (protocol === 'http:') {\r\n      url.protocol = 'https:';\r\n      shouldRedirect = true;\r\n    }\r\n\r\n    // Perform redirect if needed\r\n    if (shouldRedirect) {\r\n      return NextResponse.redirect(url.toString(), 301); // Permanent redirect\r\n    }\r\n  }\r\n  // --- Domain and HTTPS Redirect Logic END ---\r\n\r\n  // --- Rate Limiting Logic START ---\r\n  // Apply rate limiting to API routes only (skip webhooks)\r\n  if (request.nextUrl.pathname.startsWith(\"/api/\") && !request.nextUrl.pathname.startsWith(\"/api/webhooks/\")) {\r\n    // Skip rate limiting if Redis is not configured\r\n    if (!ratelimit) {\r\n      console.warn(\"Rate limiting skipped: Redis not configured\");\r\n    } else {\r\n      // Get IP address: Check 'x-forwarded-for' header first, then fallback.\r\n      const forwardedFor = request.headers.get('x-forwarded-for');\r\n      // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.\r\n      const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : \"127.0.0.1\";\r\n\r\n      try {\r\n        // Use Upstash rate limiting\r\n        const { success, limit, remaining, reset } = await ratelimit.limit(ip);\r\n\r\n        if (!success) {\r\n          // Rate limit exceeded, return 429\r\n          return new NextResponse(\"Too Many Requests\", {\r\n            status: 429,\r\n            headers: {\r\n              \"X-RateLimit-Limit\": limit.toString(),\r\n              \"X-RateLimit-Remaining\": remaining.toString(),\r\n              \"X-RateLimit-Reset\": new Date(reset * 1000).toISOString(),\r\n            },\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Rate limiting error:\", error);\r\n        // If rate limiting fails, allow the request to proceed\r\n      }\r\n    }\r\n  }\r\n  // --- Rate Limiting Logic END ---\r\n\r\n\r\n  // Proceed with Supabase session update\r\n  return await updateSession(request);\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except forhe ones starting with:\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * Feel free to modify this pattern to include more paths.\r\n     */\r\n    \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\",\r\n  ],\r\n};"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AAAA;;;;;AAEA,iDAAiD;AACjD,MAAM,WAAW,QAAQ,GAAG,CAAC,sBAAsB;AACnD,MAAM,aAAa,QAAQ,GAAG,CAAC,wBAAwB;AAEvD,IAAI,CAAC,YAAY,CAAC,YAAY;IAC5B,QAAQ,KAAK,CAAC;AAChB;AAEA,MAAM,QAAQ,YAAY,aAAa,IAAI,qKAAA,CAAA,QAAK,CAAC;IAAE,KAAK;IAAU,OAAO;AAAW,KAAK;AAEzF,0BAA0B;AAC1B,MAAM,cAAc,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI;AACpE,MAAM,gBAAgB,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAExE,MAAM,YAAY,QACd,IAAI,+JAAA,CAAA,YAAS,CAAC;IACZ,OAAO;IACP,SAAS,+JAAA,CAAA,YAAS,CAAC,aAAa,CAAC,aAAa,GAAG,cAAc,EAAE,CAAC;IAClE,WAAW;IACX,QAAQ;AACV,KACA;AAGG,eAAe,WAAW,OAAoB;IACnD,gDAAgD;IAChD,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;IACjC,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,WAAW,IAAI,QAAQ;IAE7B,yFAAyF;IACzF,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gBAClB,SAAS,QAAQ,CAAC,eAClB,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC;IAE9C,uCAAmE;;IAmBnE;IACA,8CAA8C;IAE9C,oCAAoC;IACpC,yDAAyD;IACzD,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB;QAC1G,gDAAgD;QAChD,IAAI,CAAC,WAAW;YACd,QAAQ,IAAI,CAAC;QACf,OAAO;YACL,uEAAuE;YACvE,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;YACzC,wGAAwG;YACxG,MAAM,KAAK,eAAe,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK;YAE9D,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,KAAK,CAAC;gBAEnE,IAAI,CAAC,SAAS;oBACZ,kCAAkC;oBAClC,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,qBAAqB;wBAC3C,QAAQ;wBACR,SAAS;4BACP,qBAAqB,MAAM,QAAQ;4BACnC,yBAAyB,UAAU,QAAQ;4BAC3C,qBAAqB,IAAI,KAAK,QAAQ,MAAM,WAAW;wBACzD;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,uDAAuD;YACzD;QACF;IACF;IACA,kCAAkC;IAGlC,uCAAuC;IACvC,OAAO,MAAM,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD,EAAE;AAC7B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}