1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="expo.modules.devclient.test" >
4
5    <uses-sdk
5-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:5:5-74
6        android:minSdkVersion="24"
6-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:5:15-41
7        android:targetSdkVersion="35" />
7-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:5:42-71
8
9    <instrumentation
9-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:11:5-15:77
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:11:22-76
11        android:functionalTest="false"
11-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:14:22-52
12        android:handleProfiling="false"
12-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:13:22-53
13        android:label="Tests for expo.modules.devclient.test"
13-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:15:22-75
14        android:targetPackage="expo.modules.devclient.test" />
14-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:12:22-73
15
16    <queries>
16-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
17        <package android:name="host.exp.exponent" />
17-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
17-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
18        <package android:name="androidx.test.orchestrator" />
18-->[androidx.test:runner:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3a46118320aa00bdcc18a5e722406f2\transformed\runner-1.6.2\AndroidManifest.xml:25:9-62
18-->[androidx.test:runner:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3a46118320aa00bdcc18a5e722406f2\transformed\runner-1.6.2\AndroidManifest.xml:25:18-59
19        <package android:name="androidx.test.services" />
19-->[androidx.test:runner:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3a46118320aa00bdcc18a5e722406f2\transformed\runner-1.6.2\AndroidManifest.xml:26:9-58
19-->[androidx.test:runner:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3a46118320aa00bdcc18a5e722406f2\transformed\runner-1.6.2\AndroidManifest.xml:26:18-55
20        <package android:name="com.google.android.apps.common.testing.services" />
20-->[androidx.test:runner:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3a46118320aa00bdcc18a5e722406f2\transformed\runner-1.6.2\AndroidManifest.xml:27:9-83
20-->[androidx.test:runner:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3a46118320aa00bdcc18a5e722406f2\transformed\runner-1.6.2\AndroidManifest.xml:27:18-80
21    </queries>
22    <!--
23    This manifest file is used only by Gradle to configure debug-only capabilities
24    for React Native Apps.
25    -->
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
26-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:22-75
27    <uses-permission android:name="android.permission.REORDER_TASKS" />
27-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:24:5-72
27-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:24:22-69
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
29    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
30    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
31
32    <permission
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
33        android:name="expo.modules.devclient.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="expo.modules.devclient.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
37
38    <application
38-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:7:5-9:19
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="false" >
42        <uses-library android:name="android.test.runner" />
42-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:8:9-60
42-->C:\web-app\dukancard-app\node_modules\expo-dev-client\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest5116955873722771220.xml:8:23-57
43
44        <meta-data
44-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
45            android:name="org.unimodules.core.AppLoader#react-native-headless"
45-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
46            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
46-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
47        <meta-data
47-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
48            android:name="com.facebook.soloader.enabled"
48-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
49            android:value="true" />
49-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
50
51        <activity
51-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
52            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
52-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
53            android:exported="true"
53-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
54            android:launchMode="singleTask"
54-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
55            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
55-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
56            <intent-filter>
56-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
57                <action android:name="android.intent.action.VIEW" />
57-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-69
57-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:25-66
58
59                <category android:name="android.intent.category.DEFAULT" />
59-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-76
59-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:27-73
60                <category android:name="android.intent.category.BROWSABLE" />
60-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-78
60-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:27-75
61
62                <data android:scheme="expo-dev-launcher" />
62-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-60
62-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:23-57
63            </intent-filter>
64        </activity>
65        <activity
65-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
66            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
66-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
67            android:screenOrientation="portrait"
67-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
68            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
68-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
69        <activity
69-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
70            android:name="expo.modules.devmenu.DevMenuActivity"
70-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
71            android:exported="true"
71-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
72            android:launchMode="singleTask"
72-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
73            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
73-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
74            <intent-filter>
74-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
75                <action android:name="android.intent.action.VIEW" />
75-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-69
75-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:25-66
76
77                <category android:name="android.intent.category.DEFAULT" />
77-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-76
77-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:27-73
78                <category android:name="android.intent.category.BROWSABLE" />
78-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-78
78-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:27-75
79
80                <data android:scheme="expo-dev-menu" />
80-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-60
80-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:23-57
81            </intent-filter>
82        </activity>
83        <activity
83-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
84            android:name="com.facebook.react.devsupport.DevSettingsActivity"
84-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
85            android:exported="false" />
85-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
86        <activity
86-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:27:9-34:20
87            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
87-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:28:13-99
88            android:exported="true"
88-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:29:13-36
89            android:theme="@style/WhiteBackgroundTheme" >
89-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:30:13-56
90            <intent-filter android:priority="-100" >
90-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
90-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
91                <category android:name="android.intent.category.LAUNCHER" />
91-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
91-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
92            </intent-filter>
93        </activity>
94        <activity
94-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:35:9-42:20
95            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
95-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:36:13-95
96            android:exported="true"
96-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:37:13-36
97            android:theme="@style/WhiteBackgroundTheme" >
97-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:38:13-56
98            <intent-filter android:priority="-100" >
98-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
98-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
99                <category android:name="android.intent.category.LAUNCHER" />
99-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
99-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
100            </intent-filter>
101        </activity>
102        <activity
102-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:43:9-50:20
103            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
103-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:44:13-103
104            android:exported="true"
104-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:45:13-36
105            android:theme="@style/WhiteBackgroundDialogTheme" >
105-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:46:13-62
106            <intent-filter android:priority="-100" >
106-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
106-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
107                <category android:name="android.intent.category.LAUNCHER" />
107-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
107-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f3d02ef7d34f758309bbb1a04f336fb\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
108            </intent-filter>
109        </activity>
110
111        <provider
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
112            android:name="androidx.startup.InitializationProvider"
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
113            android:authorities="expo.modules.devclient.test.androidx-startup"
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
114            android:exported="false" >
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
115            <meta-data
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.emoji2.text.EmojiCompatInitializer"
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
117                android:value="androidx.startup" />
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
118            <meta-data
118-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
119-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
120                android:value="androidx.startup" />
120-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
123                android:value="androidx.startup" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
124        </provider>
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
