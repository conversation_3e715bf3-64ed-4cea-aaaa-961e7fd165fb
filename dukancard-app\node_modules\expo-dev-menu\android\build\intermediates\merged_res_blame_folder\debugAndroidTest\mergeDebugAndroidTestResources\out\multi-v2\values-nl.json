{"logs": [{"outputFile": "expo.modules.devmenu.test.expo-dev-menu-mergeDebugAndroidTestResources-35:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "38,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3712,3815,3916,4027", "endColumns": "102,100,110,98", "endOffsets": "3810,3911,4022,4121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,4817", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,4895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "30,31,32,33,34,35,36,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2903,3005,3107,3207,3307,3414,3518,5530", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3000,3102,3202,3302,3409,3513,3632,5626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\804afae0b0d5554ac4e7feda2f2ca6c6\\transformed\\react-android-0.79.5-debug\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,274,348,434,508,584,668,747,819,897,975,1049,1136,1220,1297,1368,1438,1527,1605,1690", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "126,201,269,343,429,503,579,663,742,814,892,970,1044,1131,1215,1292,1363,1433,1522,1600,1685,1759"}, "to": {"startLines": "29,37,42,43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2827,3637,4126,4194,4268,4354,4428,4504,4588,4667,4739,4900,4978,5052,5139,5223,5300,5371,5441,5631,5709,5794", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "2898,3707,4189,4263,4349,4423,4499,4583,4662,4734,4812,4973,5047,5134,5218,5295,5366,5436,5525,5704,5789,5863"}}]}]}