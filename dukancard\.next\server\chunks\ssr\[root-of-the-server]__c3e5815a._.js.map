{"version": 3, "sources": [], "sections": [{"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport async function createClient() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll();\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            );\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmV,GAChX,iHACA", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClientLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/business-validation.ts"], "sourcesContent": ["/**\r\n * Utility functions for validating business profile completeness\r\n */\r\nimport { type BusinessProfiles } from \"../../dukancard-app/src/types/database/business-profiles\";\r\n\r\n// Required fields for accessing business dashboard\r\nexport const REQUIRED_BUSINESS_FIELDS = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"business_category\",\r\n  \"contact_email\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n] as const;\r\n\r\nexport type RequiredBusinessField = (typeof REQUIRED_BUSINESS_FIELDS)[number];\r\n\r\n/**\r\n * Check if all required business fields are complete\r\n * @param profile - Business profile object\r\n * @returns Object with validation result and missing fields\r\n */\r\nexport function validateRequiredBusinessFields(\r\n  profile: Partial<BusinessProfiles> | null\r\n) {\r\n  if (!profile) {\r\n    return {\r\n      isComplete: false,\r\n      missingFields: [...REQUIRED_BUSINESS_FIELDS],\r\n      missingFieldLabels: [\r\n        \"Your name\",\r\n        \"Your title\",\r\n        \"Business name\",\r\n        \"Business category\",\r\n        \"Contact email\",\r\n        \"Primary phone\",\r\n        \"Address line\",\r\n        \"Pincode\",\r\n        \"City\",\r\n        \"State\",\r\n        \"Locality/area\",\r\n      ],\r\n    };\r\n  }\r\n\r\n  const missingFields: RequiredBusinessField[] = [];\r\n  const missingFieldLabels: string[] = [];\r\n\r\n  const fieldLabelMap: Record<RequiredBusinessField, string> = {\r\n    member_name: \"Your name\",\r\n    title: \"Your title\",\r\n    business_name: \"Business name\",\r\n    business_category: \"Business category\",\r\n    contact_email: \"Contact email\",\r\n    phone: \"Primary phone\",\r\n    address_line: \"Address line\",\r\n    pincode: \"Pincode\",\r\n    city: \"City\",\r\n    state: \"State\",\r\n    locality: \"Locality/area\",\r\n  };\r\n\r\n  REQUIRED_BUSINESS_FIELDS.forEach((field) => {\r\n    const value = profile[field as keyof BusinessProfiles];\r\n    if (!value || String(value).trim() === \"\") {\r\n      missingFields.push(field);\r\n      missingFieldLabels.push(fieldLabelMap[field]);\r\n    }\r\n  });\r\n\r\n  return {\r\n    isComplete: missingFields.length === 0,\r\n    missingFields,\r\n    missingFieldLabels,\r\n  };\r\n}\r\n\r\n/**\r\n * Generate a user-friendly message for missing fields\r\n * @param missingFieldLabels - Array of missing field labels\r\n * @returns Formatted message string\r\n */\r\nexport function generateMissingFieldsMessage(\r\n  missingFieldLabels: string[]\r\n): string {\r\n  if (missingFieldLabels.length === 0) return \"\";\r\n\r\n  if (missingFieldLabels.length === 1) {\r\n    return `Please complete your ${missingFieldLabels[0].toLowerCase()} to access the dashboard.`;\r\n  }\r\n\r\n  if (missingFieldLabels.length === 2) {\r\n    return `Please complete your ${missingFieldLabels[0].toLowerCase()} and ${missingFieldLabels[1].toLowerCase()} to access the dashboard.`;\r\n  }\r\n\r\n  const lastField = missingFieldLabels[missingFieldLabels.length - 1];\r\n  const otherFields = missingFieldLabels.slice(0, -1);\r\n\r\n  return `Please complete your ${otherFields\r\n    .map((f) => f.toLowerCase())\r\n    .join(\", \")}, and ${lastField.toLowerCase()} to access the dashboard.`;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAIM,MAAM,2BAA2B;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AASM,SAAS,+BACd,OAAyC;IAEzC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,YAAY;YACZ,eAAe;mBAAI;aAAyB;YAC5C,oBAAoB;gBAClB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,gBAAyC,EAAE;IACjD,MAAM,qBAA+B,EAAE;IAEvC,MAAM,gBAAuD;QAC3D,aAAa;QACb,OAAO;QACP,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,OAAO;QACP,cAAc;QACd,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IAEA,yBAAyB,OAAO,CAAC,CAAC;QAChC,MAAM,QAAQ,OAAO,CAAC,MAAgC;QACtD,IAAI,CAAC,SAAS,OAAO,OAAO,IAAI,OAAO,IAAI;YACzC,cAAc,IAAI,CAAC;YACnB,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAC9C;IACF;IAEA,OAAO;QACL,YAAY,cAAc,MAAM,KAAK;QACrC;QACA;IACF;AACF;AAOO,SAAS,6BACd,kBAA4B;IAE5B,IAAI,mBAAmB,MAAM,KAAK,GAAG,OAAO;IAE5C,IAAI,mBAAmB,MAAM,KAAK,GAAG;QACnC,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,EAAE,CAAC,WAAW,GAAG,yBAAyB,CAAC;IAC/F;IAEA,IAAI,mBAAmB,MAAM,KAAK,GAAG;QACnC,OAAO,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,EAAE,CAAC,WAAW,GAAG,KAAK,EAAE,kBAAkB,CAAC,EAAE,CAAC,WAAW,GAAG,yBAAyB,CAAC;IAC1I;IAEA,MAAM,YAAY,kBAAkB,CAAC,mBAAmB,MAAM,GAAG,EAAE;IACnE,MAAM,cAAc,mBAAmB,KAAK,CAAC,GAAG,CAAC;IAEjD,OAAO,CAAC,qBAAqB,EAAE,YAC5B,GAAG,CAAC,CAAC,IAAM,EAAE,WAAW,IACxB,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,WAAW,GAAG,yBAAyB,CAAC;AAC1E", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/layout.tsx"], "sourcesContent": ["import React from \"react\";\r\n// import { cookies } from \"next/headers\"; // Removed unused import\r\nimport { createClient } from \"@/utils/supabase/server\"; // Correct import name\r\nimport BusinessDashboardClientLayout from \"./components/BusinessDashboardClientLayout\";\r\nimport { validateRequiredBusinessFields, generateMissingFieldsMessage } from \"@/utils/business-validation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n// This is now a Server Component\r\nexport default async function BusinessDashboardLayout({\r\n  // Make the function async\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  // const cookieStore = cookies(); // createClient handles this\r\n  const supabase = await createClient(); // Correct function call and add await as it's async\r\n\r\n  let businessName: string | null = null;\r\n  let logoUrl: string | null = null;\r\n  let memberName: string | null = null;\r\n  let userPlan: string | null = null;\r\n  let _businessProfile: Record<string, unknown> | null = null;\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (user) {\r\n    // Get business profile data with all required fields for validation\r\n    const { data: profile, error } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(`\r\n        business_name,\r\n        logo_url,\r\n        member_name,\r\n        title,\r\n        business_category,\r\n        contact_email,\r\n        phone,\r\n        address_line,\r\n        pincode,\r\n        city,\r\n        state,\r\n        locality\r\n      `)\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    // Get subscription data to determine plan\r\n    const { data: subscription } = await supabase\r\n      .from(\"payment_subscriptions\")\r\n      .select(\"plan_id\")\r\n      .eq(\"business_profile_id\", user.id)\r\n      .order(\"created_at\", { ascending: false })\r\n      .limit(1)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(\r\n        \"Error fetching business profile in layout:\",\r\n        error.message\r\n      );\r\n      // Handle error appropriately, maybe redirect or show an error state\r\n      // For now, we'll proceed with null values\r\n    } else if (profile) {\r\n      businessName = profile.business_name;\r\n      logoUrl = profile.logo_url;\r\n      memberName = profile.member_name;\r\n      userPlan = subscription?.plan_id || \"free\";\r\n      _businessProfile = profile;\r\n\r\n      // Validate required business fields\r\n      const validation = validateRequiredBusinessFields(profile);\r\n      if (!validation.isComplete) {\r\n        // Generate a message for missing fields\r\n        const message = generateMissingFieldsMessage(validation.missingFieldLabels);\r\n\r\n        // Redirect to onboarding with message\r\n        redirect(`/onboarding?message=${encodeURIComponent(message)}`);\r\n      }\r\n    }\r\n  } else {\r\n    // This case should ideally be handled by middleware, but good to have a fallback\r\n    console.warn(\"No user found in business dashboard layout.\");\r\n  }\r\n\r\n  // Render the Client Layout component, passing fetched data as props\r\n  return (\r\n    <BusinessDashboardClientLayout\r\n      businessName={businessName}\r\n      logoUrl={logoUrl}\r\n      memberName={memberName}\r\n      userPlan={userPlan}\r\n    >\r\n      {children}\r\n    </BusinessDashboardClientLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA,mEAAmE;AACnE,wNAAwD,sBAAsB;AAC9E;AACA;AACA;AAAA;;;;;;AAGe,eAAe,wBAAwB,EACpD,0BAA0B;AAC1B,QAAQ,EAGT;IACC,8DAA8D;IAC9D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,oDAAoD;IAE3F,IAAI,eAA8B;IAClC,IAAI,UAAyB;IAC7B,IAAI,aAA4B;IAChC,IAAI,WAA0B;IAC9B,IAAI,mBAAmD;IAEvD,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,MAAM;QACR,oEAAoE;QACpE,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;MAaT,CAAC,EACA,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,0CAA0C;QAC1C,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,yBACL,MAAM,CAAC,WACP,EAAE,CAAC,uBAAuB,KAAK,EAAE,EACjC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,GACN,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CACX,8CACA,MAAM,OAAO;QAEf,oEAAoE;QACpE,0CAA0C;QAC5C,OAAO,IAAI,SAAS;YAClB,eAAe,QAAQ,aAAa;YACpC,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,WAAW,cAAc,WAAW;YACpC,mBAAmB;YAEnB,oCAAoC;YACpC,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,iCAA8B,AAAD,EAAE;YAClD,IAAI,CAAC,WAAW,UAAU,EAAE;gBAC1B,wCAAwC;gBACxC,MAAM,UAAU,CAAA,GAAA,+HAAA,CAAA,+BAA4B,AAAD,EAAE,WAAW,kBAAkB;gBAE1E,sCAAsC;gBACtC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,UAAU;YAC/D;QACF;IACF,OAAO;QACL,iFAAiF;QACjF,QAAQ,IAAI,CAAC;IACf;IAEA,oEAAoE;IACpE,qBACE,8OAAC,6LAAA,CAAA,UAA6B;QAC5B,cAAc;QACd,SAAS;QACT,YAAY;QACZ,UAAU;kBAET;;;;;;AAGP", "debugId": null}}]}