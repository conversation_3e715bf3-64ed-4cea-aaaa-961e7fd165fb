{"logs": [{"outputFile": "expo.modules.devlauncher.test.expo-dev-launcher-mergeDebugAndroidTestResources-34:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\56f5229f81b3aef285aaeda411083055\\transformed\\material-1.2.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,24,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,198,270,340,405,472,542,614,683,752,834,924,1000,1068,1135,1213,1278,1345,1517,1912", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,23,29,34", "endColumns": "72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10", "endOffsets": "123,193,265,335,400,467,537,609,678,747,829,919,995,1063,1130,1208,1273,1340,1512,1907,2176"}, "to": {"startLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,27,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,337,407,479,549,614,681,751,823,892,961,1043,1133,1209,1277,1344,1422,1487,1554,1726,2121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,32,37", "endColumns": "72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10", "endOffsets": "332,402,474,544,609,676,746,818,887,956,1038,1128,1204,1272,1339,1417,1482,1549,1721,2116,2385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}]}