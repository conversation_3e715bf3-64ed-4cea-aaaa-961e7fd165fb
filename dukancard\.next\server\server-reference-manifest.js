self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"004cc80f306f3db1d62a7949054f8cad8e605e0bfd\": {\n      \"workers\": {\n        \"app/(auth)/choose-role/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/choose-role/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(auth)/choose-role/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/choose-role/page\": \"action-browser\",\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"7023dd6215270a75db67ac1d595e9ec8f493978c41\": {\n      \"workers\": {\n        \"app/(auth)/choose-role/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/choose-role/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/(auth)/choose-role/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/choose-role/page\": \"action-browser\"\n      }\n    },\n    \"40ace37d77c932a2d6a1b9ff05e631853479034dcc\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"40911fbf8e484719d2ce40d6ee12c917b5fe5ec063\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"60f8aa9ba7d79fc5bee2977e8e8df8daac68f26739\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"4060fe066cc4a8643132e0c9b9f4bc1a34f95f37e8\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"40bc2c7f1d5e490ee84d0fd79a01050b3dcbb955be\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"40a4c93de6e906afff26e967713285b5ecafff1751\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    },\n    \"401d867b0b3319d8655905db948907f6840df3a360\": {\n      \"workers\": {\n        \"app/(dashboard)/dashboard/business/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/business/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE4 => \\\"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(dashboard)/dashboard/business/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"m6rxUydiyelJpkv/Fnbvy/gQsTURildpaiq7S66Ed+I=\"\n}"