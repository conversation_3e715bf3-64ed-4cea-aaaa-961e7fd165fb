{"logs": [{"outputFile": "expo.modules.devlauncher.test.expo-dev-launcher-mergeDebugAndroidTestResources-34:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,13,14,15,325,326,327,328,371,374", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1020,1084,1151,23698,23814,23940,24066,26955,27127", "endLines": "2,13,14,15,325,326,327,328,373,378", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1079,1146,1210,23809,23935,24061,24189,27122,27474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\56f5229f81b3aef285aaeda411083055\\transformed\\material-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,22,25,28,31,34,37,38,41,46,57,63,69,75,81,87,88,89,93,96,99,102,105,109,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,290,378,466,554,641,728,815,902,995,1102,1207,1326,1539,1798,2069,2287,2519,2755,3005,3121,3291,3612,4641,5098,5440,5784,6134,6484,6628,6784,7177,7395,7617,7843,8059,8300,8559", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,18,21,24,27,30,33,36,37,40,45,56,62,68,74,80,86,87,88,92,95,98,101,104,108,112,115", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,143,155,10,10,10,10,10,10,10,10", "endOffsets": "118,197,285,373,461,549,636,723,810,897,990,1097,1202,1321,1534,1793,2064,2282,2514,2750,3000,3116,3286,3607,4636,5093,5435,5779,6129,6479,6623,6779,7172,7390,7612,7838,8054,8295,8554,8731"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,76,77,78,79,81,84,87,182,185,188,191,261,262,265,270,281,329,335,341,347,353,354,355,359,362,365,368,379,383,387", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,241,320,408,496,584,672,759,846,933,6582,6675,6782,6887,7109,7322,7581,13762,13980,14212,14448,19453,19569,19739,20060,21089,24194,24486,24780,25080,25380,25524,25680,26073,26291,26513,26739,27479,27720,27979", "endLines": "3,4,5,6,7,8,9,10,11,12,76,77,78,79,83,86,89,184,187,190,193,261,264,269,280,286,334,340,346,352,353,354,358,361,364,367,370,382,386,389", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,143,155,10,10,10,10,10,10,10,10", "endOffsets": "236,315,403,491,579,667,754,841,928,1015,6670,6777,6882,7001,7317,7576,7847,13975,14207,14443,14693,19564,19734,20055,21084,21541,24481,24775,25075,25375,25519,25675,26068,26286,26508,26734,26950,27715,27974,28151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,33,34,35,36,38,40,41,42,43,44,46,48,50,52,54,56,57,62,64,66,67,68,70,72,73,74,75,80,90,133,136,179,194,197,199,201,203,206,210,213,214,215,218,219,220,221,222,223,226,227,229,231,233,235,239,241,242,243,244,246,250,252,254,255,256,257,258,259,287,288,289,299,300,301,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1215,1306,1409,1512,1617,1724,1833,1942,2051,2160,2269,2376,2479,2598,2753,2908,3013,3134,3235,3382,3523,3626,3745,3852,3955,4110,4281,4430,4595,4752,4903,5022,5373,5522,5671,5783,5930,6083,6230,6305,6394,6481,7006,7852,10610,10795,13565,14698,14897,15020,15143,15256,15439,15694,15895,15984,16095,16328,16429,16524,16647,16776,16893,17070,17169,17304,17447,17582,17701,17902,18021,18114,18225,18281,18388,18583,18694,18827,18922,19013,19104,19197,19314,21546,21617,21700,22323,22380,22438,23062", "endLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,30,32,33,34,35,37,39,40,41,42,43,45,47,49,51,53,55,56,61,63,65,66,67,69,71,72,73,74,75,80,132,135,178,181,196,198,200,202,205,209,212,213,214,217,218,219,220,221,222,225,226,228,230,232,234,238,240,241,242,243,245,249,251,253,254,255,256,257,258,260,287,288,298,299,300,312,324", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1301,1404,1507,1612,1719,1828,1937,2046,2155,2264,2371,2474,2593,2748,2903,3008,3129,3230,3377,3518,3621,3740,3847,3950,4105,4276,4425,4590,4747,4898,5017,5368,5517,5666,5778,5925,6078,6225,6300,6389,6476,6577,7104,10605,10790,13560,13757,14892,15015,15138,15251,15434,15689,15890,15979,16090,16323,16424,16519,16642,16771,16888,17065,17164,17299,17442,17577,17696,17897,18016,18109,18220,18276,18383,18578,18689,18822,18917,19008,19099,19192,19309,19448,21612,21695,22318,22375,22433,23057,23693"}}]}]}