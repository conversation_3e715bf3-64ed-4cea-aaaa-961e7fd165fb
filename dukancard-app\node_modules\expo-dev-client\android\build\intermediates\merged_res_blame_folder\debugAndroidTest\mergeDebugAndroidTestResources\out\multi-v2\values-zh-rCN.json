{"logs": [{"outputFile": "expo.modules.devclient.test.expo-dev-client-mergeDebugAndroidTestResources-35:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "30,31,32,33,34,35,36,62", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2728,2820,2921,3015,3109,3202,3296,5259", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "2815,2916,3010,3104,3197,3291,3387,5355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,4593", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,4667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "38,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3462,3545,3637,3738", "endColumns": "82,91,100,92", "endOffsets": "3540,3632,3733,3826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\804afae0b0d5554ac4e7feda2f2ca6c6\\transformed\\react-android-0.79.5-debug\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,191,258,324,399,464,529,598,669,742,814,882,953,1026,1098,1175,1251,1323,1393,1462,1540,1608,1679,1746", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "116,186,253,319,394,459,524,593,664,737,809,877,948,1021,1093,1170,1246,1318,1388,1457,1535,1603,1674,1741,1810"}, "to": {"startLines": "29,37,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2662,3392,3831,3898,3964,4039,4104,4169,4238,4309,4382,4454,4522,4672,4745,4817,4894,4970,5042,5112,5181,5360,5428,5499,5566", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "2723,3457,3893,3959,4034,4099,4164,4233,4304,4377,4449,4517,4588,4740,4812,4889,4965,5037,5107,5176,5254,5423,5494,5561,5630"}}]}]}