{"logs": [{"outputFile": "expo.modules.devlauncher.test.expo-dev-launcher-mergeDebugAndroidTestResources-34:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\02014e6ae21bddbc8831a1642129fc1d\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "3232", "endLines": "52", "endColumns": "12", "endOffsets": "3377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\56f5229f81b3aef285aaeda411083055\\transformed\\material-1.2.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,320", "endLines": "5,9", "endColumns": "10,10", "endOffsets": "315,588"}, "to": {"startLines": "53,57", "startColumns": "4,4", "startOffsets": "3382,3647", "endLines": "56,60", "endColumns": "10,10", "endOffsets": "3642,3915"}}]}]}