expo.modules.devclient.test:xml/standalone_badge_gravity_bottom_start = 0x7f110004
expo.modules.devclient.test:xml/standalone_badge = 0x7f110002
expo.modules.devclient.test:styleable/ViewBackgroundHelper = 0x7f100064
expo.modules.devclient.test:styleable/View = 0x7f100063
expo.modules.devclient.test:styleable/ThemeEnforcement = 0x7f100060
expo.modules.devclient.test:styleable/TextAppearance = 0x7f10005d
expo.modules.devclient.test:styleable/TabItem = 0x7f10005b
expo.modules.devclient.test:styleable/SwipeRefreshLayout = 0x7f100058
expo.modules.devclient.test:styleable/StateListDrawable = 0x7f100056
expo.modules.devclient.test:styleable/Snackbar = 0x7f100053
expo.modules.devclient.test:styleable/SearchView = 0x7f10004e
expo.modules.devclient.test:styleable/ScrollingViewBehavior_Layout = 0x7f10004d
expo.modules.devclient.test:styleable/ScrimInsetsFrameLayout = 0x7f10004c
expo.modules.devclient.test:styleable/NavigationView = 0x7f100046
expo.modules.devclient.test:styleable/MenuView = 0x7f100045
expo.modules.devclient.test:styleable/MenuItem = 0x7f100044
expo.modules.devclient.test:styleable/MenuGroup = 0x7f100043
expo.modules.devclient.test:styleable/MaterialRadioButton = 0x7f10003f
expo.modules.devclient.test:styleable/MaterialCheckBox = 0x7f10003e
expo.modules.devclient.test:styleable/MaterialCalendarItem = 0x7f10003c
expo.modules.devclient.test:styleable/MaterialAlertDialogTheme = 0x7f100037
expo.modules.devclient.test:styleable/MaterialAlertDialog = 0x7f100036
expo.modules.devclient.test:styleable/ListPopupWindow = 0x7f100035
expo.modules.devclient.test:styleable/Slider = 0x7f100052
expo.modules.devclient.test:styleable/LinearLayoutCompat_Layout = 0x7f100034
expo.modules.devclient.test:styleable/Insets = 0x7f100032
expo.modules.devclient.test:styleable/GradientColor = 0x7f100030
expo.modules.devclient.test:styleable/GenericDraweeHierarchy = 0x7f10002f
expo.modules.devclient.test:styleable/Fragment = 0x7f10002d
expo.modules.devclient.test:styleable/FontFamily = 0x7f10002a
expo.modules.devclient.test:styleable/FloatingActionButton_Behavior_Layout = 0x7f100028
expo.modules.devclient.test:styleable/FloatingActionButton = 0x7f100027
expo.modules.devclient.test:styleable/CoordinatorLayout_Layout = 0x7f100023
expo.modules.devclient.test:styleable/CoordinatorLayout = 0x7f100022
expo.modules.devclient.test:styleable/CompoundButton = 0x7f100021
expo.modules.devclient.test:styleable/ColorStateListItem = 0x7f100020
expo.modules.devclient.test:styleable/CollapsingToolbarLayout_Layout = 0x7f10001f
expo.modules.devclient.test:styleable/BottomSheetBehavior_Layout = 0x7f100017
expo.modules.devclient.test:styleable/BottomNavigationView = 0x7f100016
expo.modules.devclient.test:styleable/Badge = 0x7f100014
expo.modules.devclient.test:styleable/AppCompatTheme = 0x7f100012
expo.modules.devclient.test:styleable/AppCompatEmojiHelper = 0x7f10000d
expo.modules.devclient.test:styleable/AppBarLayoutStates = 0x7f10000b
expo.modules.devclient.test:styleable/AppBarLayout = 0x7f10000a
expo.modules.devclient.test:styleable/ActionBarLayout = 0x7f100001
expo.modules.devclient.test:styleable/ActionBar = 0x7f100000
expo.modules.devclient.test:style/redboxButton = 0x7f0f02d3
expo.modules.devclient.test:style/Widget.MaterialComponents.Toolbar = 0x7f0f02cd
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f0f02cb
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f0f02c6
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f0f02c4
expo.modules.devclient.test:styleable/MaterialCalendar = 0x7f10003b
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f0f02c1
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f0f02c0
expo.modules.devclient.test:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f0f02bc
expo.modules.devclient.test:style/Widget.MaterialComponents.Slider = 0x7f0f02b9
expo.modules.devclient.test:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f0f02b7
expo.modules.devclient.test:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f0f02b6
expo.modules.devclient.test:style/Widget.MaterialComponents.PopupMenu = 0x7f0f02b4
expo.modules.devclient.test:style/Widget.MaterialComponents.NavigationView = 0x7f0f02b3
expo.modules.devclient.test:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f0f02d0
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f0f02b2
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f0f02ae
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f0f02ac
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f0f02ab
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f0f02aa
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f0f02a5
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f0f02a4
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f0f02a3
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f0f02a2
expo.modules.devclient.test:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f0f029f
expo.modules.devclient.test:style/Widget.MaterialComponents.Chip.Filter = 0x7f0f0297
expo.modules.devclient.test:style/Widget.MaterialComponents.Chip.Entry = 0x7f0f0296
expo.modules.devclient.test:style/Widget.MaterialComponents.Chip.Action = 0x7f0f0294
expo.modules.devclient.test:style/Widget.MaterialComponents.CheckedTextView = 0x7f0f0293
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f0f0290
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f0f028d
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.TextButton = 0x7f0f028a
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.Icon = 0x7f0f0287
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomSheet = 0x7f0f0284
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f0f0283
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomAppBar = 0x7f0f027e
expo.modules.devclient.test:style/Widget.MaterialComponents.Badge = 0x7f0f027d
expo.modules.devclient.test:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f0f027b
expo.modules.devclient.test:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f0f027a
expo.modules.devclient.test:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f0f0275
expo.modules.devclient.test:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f0f0272
expo.modules.devclient.test:style/Widget.Design.TextInputLayout = 0x7f0f0271
expo.modules.devclient.test:style/Widget.Design.TextInputEditText = 0x7f0f0270
expo.modules.devclient.test:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f0f026d
expo.modules.devclient.test:style/Widget.Compat.NotificationActionText = 0x7f0f0266
expo.modules.devclient.test:style/Widget.Compat.NotificationActionContainer = 0x7f0f0265
expo.modules.devclient.test:style/Widget.Autofill.InlineSuggestionTitle = 0x7f0f0264
expo.modules.devclient.test:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f0f0261
expo.modules.devclient.test:style/Widget.Autofill.InlineSuggestionChip = 0x7f0f0260
expo.modules.devclient.test:style/Widget.Autofill = 0x7f0f025f
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f0f02a8
expo.modules.devclient.test:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f025e
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f0f0291
expo.modules.devclient.test:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f025c
expo.modules.devclient.test:style/Widget.AppCompat.TextView = 0x7f0f025b
expo.modules.devclient.test:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f025a
expo.modules.devclient.test:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f0258
expo.modules.devclient.test:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0256
expo.modules.devclient.test:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0252
expo.modules.devclient.test:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f024c
expo.modules.devclient.test:style/Widget.AppCompat.ListView.DropDown = 0x7f0f0249
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f0f028c
expo.modules.devclient.test:style/Widget.AppCompat.ListMenuView = 0x7f0f0246
expo.modules.devclient.test:style/Widget.AppCompat.Light.SearchView = 0x7f0f0244
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar = 0x7f0f0230
expo.modules.devclient.test:style/Widget.AppCompat.EditText = 0x7f0f022e
expo.modules.devclient.test:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f022d
expo.modules.devclient.test:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f0f02ce
expo.modules.devclient.test:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f022c
expo.modules.devclient.test:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f022b
expo.modules.devclient.test:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f022a
expo.modules.devclient.test:style/Widget.AppCompat.ButtonBar = 0x7f0f0227
expo.modules.devclient.test:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0224
expo.modules.devclient.test:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0223
expo.modules.devclient.test:style/Widget.AppCompat.ActivityChooserView = 0x7f0f021f
expo.modules.devclient.test:style/Widget.AppCompat.ActionMode = 0x7f0f021e
expo.modules.devclient.test:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f021d
expo.modules.devclient.test:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f021c
expo.modules.devclient.test:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f0219
expo.modules.devclient.test:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f0218
expo.modules.devclient.test:style/ThemeOverlayColorAccentRed = 0x7f0f0213
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f0f0212
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f0f0210
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f0f020c
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f0f020b
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f0f0209
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f0f0208
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f0f0205
expo.modules.devclient.test:style/Widget.MaterialComponents.Button = 0x7f0f0286
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f0f01fe
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f0f01fd
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f0f01fc
expo.modules.devclient.test:styleable/PopupWindowBackgroundState = 0x7f100048
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f0f01f8
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f0f01f7
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f0f01f6
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f0f01f5
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f0f01f4
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f0f01f2
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f0f01f0
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f0f01ef
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents = 0x7f0f01ee
expo.modules.devclient.test:style/ThemeOverlay.Design.TextInputEditText = 0x7f0f01ed
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f01eb
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f01e9
expo.modules.devclient.test:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f0220
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f01e8
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f01e7
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.Dark = 0x7f0f01e6
expo.modules.devclient.test:style/ThemeOverlay.AppCompat = 0x7f0f01e4
expo.modules.devclient.test:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f0f01e2
expo.modules.devclient.test:style/Theme.ReactNative.AppCompat.Light = 0x7f0f01e1
expo.modules.devclient.test:style/Theme.MaterialComponents.NoActionBar = 0x7f0f01df
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f0f01db
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f0f01d9
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f0f01d7
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f0f01d4
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog = 0x7f0f01d3
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Bridge = 0x7f0f01d0
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.BarSize = 0x7f0f01ce
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f0f01cb
expo.modules.devclient.test:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0245
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f0f01c9
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f0f01c7
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f0f01c3
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f0f01c1
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f0f01be
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f0f01bd
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f0f01b9
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f0f01b5
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight = 0x7f0f01b4
expo.modules.devclient.test:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f0f01b1
expo.modules.devclient.test:style/Theme.FullScreenDialogAnimatedFade = 0x7f0f01ae
expo.modules.devclient.test:style/Theme.DevLauncher.LauncherActivity = 0x7f0f01ac
expo.modules.devclient.test:style/Theme.Design.Light.NoActionBar = 0x7f0f01a9
expo.modules.devclient.test:style/Theme.Design.Light.BottomSheetDialog = 0x7f0f01a8
expo.modules.devclient.test:style/Theme.Design = 0x7f0f01a5
expo.modules.devclient.test:style/Widget.AppCompat.PopupWindow = 0x7f0f024d
expo.modules.devclient.test:style/Theme.Catalyst.RedBox = 0x7f0f01a4
expo.modules.devclient.test:style/Theme.Catalyst = 0x7f0f01a2
expo.modules.devclient.test:style/Theme.AppCompat.NoActionBar = 0x7f0f019f
expo.modules.devclient.test:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f019d
expo.modules.devclient.test:style/Theme.AppCompat.Light.Dialog = 0x7f0f019a
expo.modules.devclient.test:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0196
expo.modules.devclient.test:style/Theme.AppCompat.Dialog = 0x7f0f0193
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0233
expo.modules.devclient.test:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f0192
expo.modules.devclient.test:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f0191
expo.modules.devclient.test:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f018e
expo.modules.devclient.test:style/Theme.AppCompat = 0x7f0f018a
expo.modules.devclient.test:style/Theme = 0x7f0f0189
expo.modules.devclient.test:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0186
expo.modules.devclient.test:styleable/Spinner = 0x7f100055
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Tooltip = 0x7f0f0185
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f0f0184
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Overline = 0x7f0f0182
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Headline4 = 0x7f0f017f
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f0f01b8
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Headline3 = 0x7f0f017e
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Headline2 = 0x7f0f017d
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Chip = 0x7f0f017b
expo.modules.devclient.test:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0241
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Caption = 0x7f0f017a
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f0f01f3
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Body2 = 0x7f0f0178
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Body1 = 0x7f0f0177
expo.modules.devclient.test:style/TextAppearance.Design.Tab = 0x7f0f0175
expo.modules.devclient.test:style/TextAppearance.Design.Snackbar.Message = 0x7f0f0173
expo.modules.devclient.test:style/TextAppearance.Design.Placeholder = 0x7f0f0171
expo.modules.devclient.test:style/TextAppearance.Design.Hint = 0x7f0f0170
expo.modules.devclient.test:style/TextAppearance.Design.Error = 0x7f0f016e
expo.modules.devclient.test:style/TextAppearance.Design.Counter = 0x7f0f016c
expo.modules.devclient.test:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f0f016b
expo.modules.devclient.test:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f0168
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0165
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f0164
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0161
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f015b
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f0159
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f0158
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f0155
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0154
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0153
expo.modules.devclient.test:style/TextAppearance.AppCompat.Tooltip = 0x7f0f0152
expo.modules.devclient.test:style/TextAppearance.AppCompat.Title = 0x7f0f0150
expo.modules.devclient.test:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f014f
expo.modules.devclient.test:style/TextAppearance.AppCompat.Subhead = 0x7f0f014e
expo.modules.devclient.test:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f014d
expo.modules.devclient.test:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f014b
expo.modules.devclient.test:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f0148
expo.modules.devclient.test:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f0143
expo.modules.devclient.test:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f0142
expo.modules.devclient.test:style/TextAppearance.AppCompat.Large = 0x7f0f0141
expo.modules.devclient.test:style/TextAppearance.AppCompat.Display4 = 0x7f0f013e
expo.modules.devclient.test:style/TextAppearance.AppCompat.Display2 = 0x7f0f013c
expo.modules.devclient.test:style/TextAppearance.AppCompat.Display1 = 0x7f0f013b
expo.modules.devclient.test:style/TextAppearance.AppCompat.Caption = 0x7f0f013a
expo.modules.devclient.test:style/TextAppearance.AppCompat.Button = 0x7f0f0139
expo.modules.devclient.test:style/TextAppearance.AppCompat.Body2 = 0x7f0f0138
expo.modules.devclient.test:style/TextAppearance.AppCompat.Body1 = 0x7f0f0137
expo.modules.devclient.test:style/TextAppearance.AppCompat = 0x7f0f0136
expo.modules.devclient.test:style/TestThemeWithLineHeightDisabled = 0x7f0f0135
expo.modules.devclient.test:style/TestThemeWithLineHeight = 0x7f0f0134
expo.modules.devclient.test:styleable/SwitchCompat = 0x7f100059
expo.modules.devclient.test:style/TestStyleWithThemeLineHeightAttribute = 0x7f0f0132
expo.modules.devclient.test:style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f0f012f
expo.modules.devclient.test:style/Test.Widget.MaterialComponents.MaterialCalendar.Day = 0x7f0f012e
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0235
expo.modules.devclient.test:style/Test.Widget.MaterialComponents.MaterialCalendar = 0x7f0f012d
expo.modules.devclient.test:style/SpinnerDatePickerStyle = 0x7f0f012a
expo.modules.devclient.test:style/SpinnerDatePickerDialog = 0x7f0f0129
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f0f0126
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f0f0123
expo.modules.devclient.test:style/ShapeAppearanceOverlay.Cut = 0x7f0f011d
expo.modules.devclient.test:style/ShapeAppearanceOverlay.BottomRightCut = 0x7f0f011c
expo.modules.devclient.test:style/ShapeAppearanceOverlay = 0x7f0f011a
expo.modules.devclient.test:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f0f0119
expo.modules.devclient.test:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f0f0115
expo.modules.devclient.test:style/ShapeAppearance.MaterialComponents = 0x7f0f0114
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f0111
expo.modules.devclient.test:style/Theme.DevLauncher.ErrorActivity = 0x7f0f01ab
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f0110
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f010f
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f010e
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f010c
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f010b
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f010a
expo.modules.devclient.test:style/Widget.Design.CollapsingToolbar = 0x7f0f026a
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f0108
expo.modules.devclient.test:style/Theme.MaterialComponents.Light = 0x7f0f01cd
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f0107
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f0106
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f0105
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f0104
expo.modules.devclient.test:style/Platform.V25.AppCompat = 0x7f0f0100
expo.modules.devclient.test:style/Theme.Design.NoActionBar = 0x7f0f01aa
expo.modules.devclient.test:style/Test.Theme.MaterialComponents.MaterialCalendar = 0x7f0f012c
expo.modules.devclient.test:style/Platform.V21.AppCompat = 0x7f0f00fe
expo.modules.devclient.test:styleable/Tooltip = 0x7f100062
expo.modules.devclient.test:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f00fc
expo.modules.devclient.test:style/Platform.ThemeOverlay.AppCompat = 0x7f0f00fb
expo.modules.devclient.test:style/Platform.MaterialComponents.Light.Dialog = 0x7f0f00fa
expo.modules.devclient.test:style/Platform.MaterialComponents.Dialog = 0x7f0f00f8
expo.modules.devclient.test:style/Platform.MaterialComponents = 0x7f0f00f7
expo.modules.devclient.test:style/Platform.AppCompat = 0x7f0f00f5
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f0f00f2
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f0f00f1
expo.modules.devclient.test:styleable/CheckedTextView = 0x7f10001b
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f0f00ee
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f0f01d1
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f0f00ed
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f0f00ec
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f0f00eb
expo.modules.devclient.test:style/EmptyTheme = 0x7f0f00e9
expo.modules.devclient.test:style/DialogAnimationSlide = 0x7f0f00e8
expo.modules.devclient.test:style/CardView.Dark = 0x7f0f00e5
expo.modules.devclient.test:style/CardView = 0x7f0f00e4
expo.modules.devclient.test:styleable/AppCompatTextView = 0x7f100011
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.Slider = 0x7f0f00de
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f0f00dd
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.Alert = 0x7f0f01c5
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f0f00dc
expo.modules.devclient.test:style/Base.Widget.Design.TabLayout = 0x7f0f00d6
expo.modules.devclient.test:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f00d1
expo.modules.devclient.test:style/Base.Widget.AppCompat.SeekBar = 0x7f0f00ce
expo.modules.devclient.test:style/Base.Widget.AppCompat.SearchView = 0x7f0f00cc
expo.modules.devclient.test:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f00ca
expo.modules.devclient.test:style/Base.Widget.AppCompat.RatingBar = 0x7f0f00c9
expo.modules.devclient.test:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f00c7
expo.modules.devclient.test:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f00c6
expo.modules.devclient.test:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f00c4
expo.modules.devclient.test:style/Base.Widget.AppCompat.ListView = 0x7f0f00c1
expo.modules.devclient.test:styleable/ActionMenuItemView = 0x7f100002
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f00bc
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f00ba
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f00b9
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f00b8
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f00b7
expo.modules.devclient.test:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f0f0299
expo.modules.devclient.test:style/Base.Widget.AppCompat.EditText = 0x7f0f00b5
expo.modules.devclient.test:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f00b1
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f0f00df
expo.modules.devclient.test:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f00ad
expo.modules.devclient.test:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f00ab
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f0f0183
expo.modules.devclient.test:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f00aa
expo.modules.devclient.test:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f00a9
expo.modules.devclient.test:styleable/GradientColorItem = 0x7f100031
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionMode = 0x7f0f00a4
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00a3
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f00a2
expo.modules.devclient.test:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f023f
expo.modules.devclient.test:style/Theme.AutofillInlineSuggestion = 0x7f0f01a1
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionButton = 0x7f0f00a1
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f009e
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f009d
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionBar = 0x7f0f009c
expo.modules.devclient.test:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f009a
expo.modules.devclient.test:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0099
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f0f01cf
expo.modules.devclient.test:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f0098
expo.modules.devclient.test:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f0095
expo.modules.devclient.test:style/Base.V7.Theme.AppCompat = 0x7f0f0094
expo.modules.devclient.test:style/Base.V28.Theme.AppCompat = 0x7f0f0092
expo.modules.devclient.test:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f0091
expo.modules.devclient.test:style/Base.V23.Theme.AppCompat.Light = 0x7f0f008e
expo.modules.devclient.test:style/Base.V23.Theme.AppCompat = 0x7f0f008d
expo.modules.devclient.test:style/Base.V22.Theme.AppCompat.Light = 0x7f0f008c
expo.modules.devclient.test:style/Base.V22.Theme.AppCompat = 0x7f0f008b
expo.modules.devclient.test:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f0f0089
expo.modules.devclient.test:style/Base.V21.Theme.MaterialComponents.Light = 0x7f0f0088
expo.modules.devclient.test:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00d5
expo.modules.devclient.test:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f0f0087
expo.modules.devclient.test:styleable/Autofill.InlineSuggestion = 0x7f100013
expo.modules.devclient.test:style/Base.V21.Theme.MaterialComponents = 0x7f0f0086
expo.modules.devclient.test:style/Base.V21.Theme.AppCompat = 0x7f0f0082
expo.modules.devclient.test:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f0f007f
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f0f01bc
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f0f007e
expo.modules.devclient.test:style/Widget.AppCompat.ListView.Menu = 0x7f0f024a
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f023c
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f0f007c
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f0f007b
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Light = 0x7f0f007a
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f0f0079
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents = 0x7f0f0076
expo.modules.devclient.test:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f0f0075
expo.modules.devclient.test:style/Theme.FullScreenDialog = 0x7f0f01ad
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f006e
expo.modules.devclient.test:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f019c
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f006c
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat = 0x7f0f006a
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f0f0065
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f0f0064
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f00bb
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f0f0063
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Button = 0x7f0f0179
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f0f0062
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f0f005f
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f0f0059
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Bridge = 0x7f0f0058
expo.modules.devclient.test:styleable/TextInputLayout = 0x7f10005f
expo.modules.devclient.test:style/Base.Theme.MaterialComponents = 0x7f0f0057
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0055
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0054
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0052
expo.modules.devclient.test:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0145
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0051
expo.modules.devclient.test:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f004f
expo.modules.devclient.test:style/Widget.Design.Snackbar = 0x7f0f026e
expo.modules.devclient.test:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f004c
expo.modules.devclient.test:style/Base.Theme.AppCompat.Dialog = 0x7f0f004b
expo.modules.devclient.test:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f0048
expo.modules.devclient.test:style/Base.Widget.AppCompat.Spinner = 0x7f0f00d0
expo.modules.devclient.test:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f0047
expo.modules.devclient.test:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f0f0045
expo.modules.devclient.test:style/Theme.Design.BottomSheetDialog = 0x7f0f01a6
expo.modules.devclient.test:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f0f0044
expo.modules.devclient.test:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f0085
expo.modules.devclient.test:style/Base.TextAppearance.MaterialComponents.Button = 0x7f0f0043
expo.modules.devclient.test:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f0f0042
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f003f
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f003e
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f003c
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f003b
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f003a
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0039
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f0034
expo.modules.devclient.test:style/Base.V28.Theme.AppCompat.Light = 0x7f0f0093
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0032
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0030
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f002d
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f002c
expo.modules.devclient.test:styleable/SwitchMaterial = 0x7f10005a
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Small = 0x7f0f002a
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f0028
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f0025
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0020
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar = 0x7f0f02a1
expo.modules.devclient.test:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f00cb
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f001f
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f001d
expo.modules.devclient.test:style/Theme.Catalyst.LogBox = 0x7f0f01a3
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f001a
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f0017
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f0016
expo.modules.devclient.test:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f000d
expo.modules.devclient.test:style/Base.AlertDialog.AppCompat.Light = 0x7f0f000b
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f006f
expo.modules.devclient.test:style/Base.AlertDialog.AppCompat = 0x7f0f000a
expo.modules.devclient.test:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f0f0009
expo.modules.devclient.test:style/Animation.Catalyst.RedBox = 0x7f0f0007
expo.modules.devclient.test:style/Animation.Catalyst.LogBox = 0x7f0f0006
expo.modules.devclient.test:style/Animation.AppCompat.Tooltip = 0x7f0f0005
expo.modules.devclient.test:style/Animation.AppCompat.DropDownUp = 0x7f0f0004
expo.modules.devclient.test:style/AlertDialog.AppCompat.Light = 0x7f0f0001
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0163
expo.modules.devclient.test:style/AlertDialog.AppCompat = 0x7f0f0000
expo.modules.devclient.test:string/state_on_description = 0x7f0e0092
expo.modules.devclient.test:string/state_off_description = 0x7f0e0091
expo.modules.devclient.test:string/state_expanded_description = 0x7f0e008f
expo.modules.devclient.test:string/state_busy_description = 0x7f0e008d
expo.modules.devclient.test:string/splash_screen_text = 0x7f0e008c
expo.modules.devclient.test:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f0f0074
expo.modules.devclient.test:string/scrollbar_description = 0x7f0e0089
expo.modules.devclient.test:string/progressbar_description = 0x7f0e0086
expo.modules.devclient.test:string/path_password_strike_through = 0x7f0e0085
expo.modules.devclient.test:string/path_password_eye_mask_visible = 0x7f0e0084
expo.modules.devclient.test:string/path_password_eye_mask_strike_through = 0x7f0e0083
expo.modules.devclient.test:string/path_password_eye = 0x7f0e0082
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f0f00e0
expo.modules.devclient.test:string/password_toggle_content_description = 0x7f0e0081
expo.modules.devclient.test:string/mtrl_picker_toggle_to_text_input_mode = 0x7f0e007f
expo.modules.devclient.test:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f0e007d
expo.modules.devclient.test:string/mtrl_picker_text_input_month_abbr = 0x7f0e007b
expo.modules.devclient.test:string/mtrl_picker_text_input_day_abbr = 0x7f0e007a
expo.modules.devclient.test:string/mtrl_picker_text_input_date_range_end_hint = 0x7f0e0078
expo.modules.devclient.test:string/mtrl_picker_range_header_unselected = 0x7f0e0075
expo.modules.devclient.test:string/mtrl_picker_range_header_selected = 0x7f0e0073
expo.modules.devclient.test:string/mtrl_picker_range_header_only_start_selected = 0x7f0e0072
expo.modules.devclient.test:string/mtrl_picker_range_header_title = 0x7f0e0074
expo.modules.devclient.test:string/mtrl_picker_navigate_to_year_description = 0x7f0e006f
expo.modules.devclient.test:string/mtrl_picker_invalid_range = 0x7f0e006e
expo.modules.devclient.test:string/mtrl_picker_invalid_format_example = 0x7f0e006c
expo.modules.devclient.test:string/mtrl_picker_invalid_format = 0x7f0e006b
expo.modules.devclient.test:string/mtrl_picker_date_header_unselected = 0x7f0e0069
expo.modules.devclient.test:string/mtrl_picker_date_header_title = 0x7f0e0068
expo.modules.devclient.test:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f021a
expo.modules.devclient.test:string/mtrl_picker_cancel = 0x7f0e0065
expo.modules.devclient.test:string/mtrl_picker_announce_current_selection = 0x7f0e0064
expo.modules.devclient.test:string/mtrl_picker_a11y_prev_month = 0x7f0e0063
expo.modules.devclient.test:style/TextAppearance.Design.Counter.Overflow = 0x7f0f016d
expo.modules.devclient.test:string/mtrl_picker_a11y_next_month = 0x7f0e0062
expo.modules.devclient.test:string/mtrl_exceed_max_badge_number_content_description = 0x7f0e0060
expo.modules.devclient.test:string/mtrl_chip_close_icon_content_description = 0x7f0e005f
expo.modules.devclient.test:string/menubar_description = 0x7f0e005c
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f0f02a0
expo.modules.devclient.test:string/material_slider_range_end = 0x7f0e0059
expo.modules.devclient.test:string/link_description = 0x7f0e0058
expo.modules.devclient.test:string/item_view_role_description = 0x7f0e0057
expo.modules.devclient.test:string/image_description = 0x7f0e0055
expo.modules.devclient.test:string/hide_bottom_view_on_scroll_behavior = 0x7f0e0053
expo.modules.devclient.test:string/header_description = 0x7f0e0052
expo.modules.devclient.test:string/fallback_menu_item_open_in_browser = 0x7f0e0050
expo.modules.devclient.test:string/error_icon_content_description = 0x7f0e004b
expo.modules.devclient.test:string/dev_launcher_reload = 0x7f0e004a
expo.modules.devclient.test:string/dev_launcher_go_to_home = 0x7f0e0049
expo.modules.devclient.test:string/dev_launcher_error_header = 0x7f0e0048
expo.modules.devclient.test:string/dev_launcher_error_details = 0x7f0e0047
expo.modules.devclient.test:string/character_counter_pattern = 0x7f0e0042
expo.modules.devclient.test:string/character_counter_content_description = 0x7f0e0040
expo.modules.devclient.test:string/catalyst_settings_title = 0x7f0e003f
expo.modules.devclient.test:string/catalyst_sample_profiler_toggle = 0x7f0e003d
expo.modules.devclient.test:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f0f0262
expo.modules.devclient.test:string/catalyst_report_button = 0x7f0e003c
expo.modules.devclient.test:string/catalyst_reload_error = 0x7f0e003b
expo.modules.devclient.test:string/catalyst_reload_button = 0x7f0e003a
expo.modules.devclient.test:string/catalyst_reload = 0x7f0e0039
expo.modules.devclient.test:string/catalyst_inspector_toggle = 0x7f0e0034
expo.modules.devclient.test:string/catalyst_hot_reloading_auto_disable = 0x7f0e0031
expo.modules.devclient.test:string/catalyst_hot_reloading = 0x7f0e0030
expo.modules.devclient.test:string/catalyst_heap_capture = 0x7f0e002f
expo.modules.devclient.test:string/catalyst_dismiss_button = 0x7f0e002e
expo.modules.devclient.test:string/catalyst_dev_menu_header = 0x7f0e002c
expo.modules.devclient.test:string/catalyst_debug_open_disabled = 0x7f0e002b
expo.modules.devclient.test:style/Widget.Design.BottomSheet.Modal = 0x7f0f0269
expo.modules.devclient.test:string/catalyst_debug_connecting = 0x7f0e0028
expo.modules.devclient.test:string/call_notification_ongoing_text = 0x7f0e0024
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f0f0069
expo.modules.devclient.test:string/catalyst_dev_menu_sub_header = 0x7f0e002d
expo.modules.devclient.test:string/call_notification_decline_action = 0x7f0e0021
expo.modules.devclient.test:style/Widget.AppCompat.Button.Small = 0x7f0f0226
expo.modules.devclient.test:string/appbar_scrolling_view_behavior = 0x7f0e001d
expo.modules.devclient.test:string/androidx_startup = 0x7f0e001c
expo.modules.devclient.test:string/alert_description = 0x7f0e001b
expo.modules.devclient.test:string/abc_shareactionprovider_share_with_application = 0x7f0e0019
expo.modules.devclient.test:string/abc_shareactionprovider_share_with = 0x7f0e0018
expo.modules.devclient.test:string/abc_searchview_description_voice = 0x7f0e0017
expo.modules.devclient.test:string/abc_searchview_description_submit = 0x7f0e0016
expo.modules.devclient.test:string/abc_searchview_description_search = 0x7f0e0015
expo.modules.devclient.test:string/abc_search_hint = 0x7f0e0012
expo.modules.devclient.test:string/abc_prepend_shortcut_label = 0x7f0e0011
expo.modules.devclient.test:string/abc_menu_space_shortcut_label = 0x7f0e000f
expo.modules.devclient.test:string/abc_menu_shift_shortcut_label = 0x7f0e000e
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f0f01bf
expo.modules.devclient.test:string/abc_menu_function_shortcut_label = 0x7f0e000c
expo.modules.devclient.test:string/abc_menu_enter_shortcut_label = 0x7f0e000b
expo.modules.devclient.test:string/abc_menu_delete_shortcut_label = 0x7f0e000a
expo.modules.devclient.test:string/abc_menu_ctrl_shortcut_label = 0x7f0e0009
expo.modules.devclient.test:string/abc_capital_on = 0x7f0e0007
expo.modules.devclient.test:string/abc_activitychooserview_choose_application = 0x7f0e0005
expo.modules.devclient.test:string/abc_activity_chooser_view_see_all = 0x7f0e0004
expo.modules.devclient.test:string/abc_action_bar_home_description = 0x7f0e0000
expo.modules.devclient.test:raw/keep = 0x7f0d0000
expo.modules.devclient.test:plurals/mtrl_badge_content_description = 0x7f0c0000
expo.modules.devclient.test:layout/text_view_without_line_height = 0x7f0b0070
expo.modules.devclient.test:layout/text_view_with_theme_line_height = 0x7f0b006f
expo.modules.devclient.test:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f00c5
expo.modules.devclient.test:layout/text_view_with_line_height_from_style = 0x7f0b006e
expo.modules.devclient.test:layout/test_toolbar_elevation = 0x7f0b006a
expo.modules.devclient.test:layout/test_toolbar_custom_background = 0x7f0b0069
expo.modules.devclient.test:layout/test_toolbar = 0x7f0b0068
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f0f0202
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog = 0x7f0f01c4
expo.modules.devclient.test:layout/test_design_radiobutton = 0x7f0b0066
expo.modules.devclient.test:layout/test_design_checkbox = 0x7f0b0065
expo.modules.devclient.test:styleable/PopupWindow = 0x7f100047
expo.modules.devclient.test:style/ShapeAppearanceOverlay.DifferentCornerSize = 0x7f0f011e
expo.modules.devclient.test:layout/test_chip_zero_corner_radius = 0x7f0b0064
expo.modules.devclient.test:layout/test_action_chip = 0x7f0b0063
expo.modules.devclient.test:layout/support_simple_spinner_dropdown_item = 0x7f0b0062
expo.modules.devclient.test:layout/select_dialog_singlechoice_material = 0x7f0b0061
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f0f02af
expo.modules.devclient.test:layout/select_dialog_multichoice_material = 0x7f0b0060
expo.modules.devclient.test:style/Base.Widget.AppCompat.TextView = 0x7f0f00d2
expo.modules.devclient.test:layout/redbox_view = 0x7f0b005e
expo.modules.devclient.test:layout/redbox_item_frame = 0x7f0b005c
expo.modules.devclient.test:layout/paused_in_debugger_view = 0x7f0b005b
expo.modules.devclient.test:layout/notification_template_icon_group = 0x7f0b0058
expo.modules.devclient.test:layout/notification_action = 0x7f0b0055
expo.modules.devclient.test:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f00a8
expo.modules.devclient.test:layout/mtrl_picker_text_input_date_range = 0x7f0b0054
expo.modules.devclient.test:layout/mtrl_picker_header_selection_text = 0x7f0b0050
expo.modules.devclient.test:layout/mtrl_picker_header_fullscreen = 0x7f0b004f
expo.modules.devclient.test:layout/mtrl_picker_header_dialog = 0x7f0b004e
expo.modules.devclient.test:layout/mtrl_picker_fullscreen = 0x7f0b004d
expo.modules.devclient.test:layout/mtrl_picker_actions = 0x7f0b004b
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f0f01de
expo.modules.devclient.test:layout/mtrl_layout_snackbar = 0x7f0b0049
expo.modules.devclient.test:layout/mtrl_calendar_year = 0x7f0b0048
expo.modules.devclient.test:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f00c2
expo.modules.devclient.test:layout/test_toolbar_surface = 0x7f0b006b
expo.modules.devclient.test:layout/mtrl_calendar_months = 0x7f0b0046
expo.modules.devclient.test:layout/test_reflow_chipgroup = 0x7f0b0067
expo.modules.devclient.test:layout/mtrl_calendar_month_navigation = 0x7f0b0045
expo.modules.devclient.test:layout/mtrl_calendar_horizontal = 0x7f0b0042
expo.modules.devclient.test:layout/mtrl_calendar_days_of_week = 0x7f0b0041
expo.modules.devclient.test:layout/mtrl_calendar_day_of_week = 0x7f0b0040
expo.modules.devclient.test:layout/mtrl_calendar_day = 0x7f0b003f
expo.modules.devclient.test:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b003e
expo.modules.devclient.test:layout/mtrl_alert_select_dialog_item = 0x7f0b003c
expo.modules.devclient.test:layout/mtrl_alert_dialog_title = 0x7f0b003b
expo.modules.devclient.test:layout/mtrl_alert_dialog_actions = 0x7f0b003a
expo.modules.devclient.test:layout/ime_secondary_split_test_activity = 0x7f0b0038
expo.modules.devclient.test:layout/fps_view = 0x7f0b0036
expo.modules.devclient.test:string/fallback_menu_item_share_link = 0x7f0e0051
expo.modules.devclient.test:layout/error_activity_content_view = 0x7f0b0032
expo.modules.devclient.test:layout/design_text_input_start_icon = 0x7f0b0030
expo.modules.devclient.test:layout/design_navigation_menu_item = 0x7f0b002e
expo.modules.devclient.test:layout/design_navigation_menu = 0x7f0b002d
expo.modules.devclient.test:style/Widget.MaterialComponents.TextView = 0x7f0f02cc
expo.modules.devclient.test:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0046
expo.modules.devclient.test:layout/design_navigation_item_header = 0x7f0b002a
expo.modules.devclient.test:styleable/AppCompatTextHelper = 0x7f100010
expo.modules.devclient.test:layout/design_navigation_item = 0x7f0b0029
expo.modules.devclient.test:layout/design_layout_snackbar = 0x7f0b0024
expo.modules.devclient.test:layout/design_bottom_sheet_dialog = 0x7f0b0023
expo.modules.devclient.test:layout/design_bottom_navigation_item = 0x7f0b0022
expo.modules.devclient.test:layout/browser_actions_context_menu_page = 0x7f0b001f
expo.modules.devclient.test:layout/abc_search_view = 0x7f0b0019
expo.modules.devclient.test:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
expo.modules.devclient.test:layout/abc_screen_simple = 0x7f0b0015
expo.modules.devclient.test:layout/abc_screen_content_include = 0x7f0b0014
expo.modules.devclient.test:styleable/AppCompatSeekBar = 0x7f10000f
expo.modules.devclient.test:styleable/AnimatedStateListDrawableCompat = 0x7f100007
expo.modules.devclient.test:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0247
expo.modules.devclient.test:style/TextAppearance.Design.HelperText = 0x7f0f016f
expo.modules.devclient.test:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
expo.modules.devclient.test:layout/abc_list_menu_item_radio = 0x7f0b0011
expo.modules.devclient.test:layout/dev_loading_view = 0x7f0b0031
expo.modules.devclient.test:layout/abc_list_menu_item_layout = 0x7f0b0010
expo.modules.devclient.test:layout/abc_list_menu_item_checkbox = 0x7f0b000e
expo.modules.devclient.test:layout/abc_expanded_menu_layout = 0x7f0b000d
expo.modules.devclient.test:layout/abc_alert_dialog_title_material = 0x7f0b000a
expo.modules.devclient.test:layout/abc_alert_dialog_material = 0x7f0b0009
expo.modules.devclient.test:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
expo.modules.devclient.test:layout/abc_activity_chooser_view = 0x7f0b0006
expo.modules.devclient.test:layout/abc_action_mode_bar = 0x7f0b0004
expo.modules.devclient.test:layout/abc_action_menu_item_layout = 0x7f0b0002
expo.modules.devclient.test:layout/abc_action_bar_up_container = 0x7f0b0001
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents = 0x7f0f00ea
expo.modules.devclient.test:interpolator/mtrl_linear_out_slow_in = 0x7f0a000a
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f0f0289
expo.modules.devclient.test:interpolator/mtrl_linear = 0x7f0a0009
expo.modules.devclient.test:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
expo.modules.devclient.test:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
expo.modules.devclient.test:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f00b3
expo.modules.devclient.test:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
expo.modules.devclient.test:integer/status_bar_notification_info_maxnum = 0x7f090015
expo.modules.devclient.test:integer/show_password_duration = 0x7f090014
expo.modules.devclient.test:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f090012
expo.modules.devclient.test:integer/mtrl_card_anim_delay_ms = 0x7f09000f
expo.modules.devclient.test:integer/mtrl_calendar_year_selector_span = 0x7f09000e
expo.modules.devclient.test:integer/mtrl_btn_anim_duration_ms = 0x7f09000b
expo.modules.devclient.test:integer/mtrl_btn_anim_delay_ms = 0x7f09000a
expo.modules.devclient.test:integer/hide_password_duration = 0x7f090008
expo.modules.devclient.test:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
expo.modules.devclient.test:integer/design_snackbar_text_max_lines = 0x7f090006
expo.modules.devclient.test:integer/config_tooltipAnimTime = 0x7f090005
expo.modules.devclient.test:integer/cancel_button_image_alpha = 0x7f090004
expo.modules.devclient.test:integer/bottom_sheet_slide_duration = 0x7f090003
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f0f028e
expo.modules.devclient.test:integer/app_bar_elevation_anim_duration = 0x7f090002
expo.modules.devclient.test:integer/abc_config_activityShortDur = 0x7f090001
expo.modules.devclient.test:integer/abc_config_activityDefaultDur = 0x7f090000
expo.modules.devclient.test:styleable/FlowLayout = 0x7f100029
expo.modules.devclient.test:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f0f027c
expo.modules.devclient.test:id/wrap_content = 0x7f080175
expo.modules.devclient.test:style/Base.Theme.AppCompat = 0x7f0f0049
expo.modules.devclient.test:id/withinBounds = 0x7f080174
expo.modules.devclient.test:id/withText = 0x7f080173
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.LargeTouch = 0x7f0f01dc
expo.modules.devclient.test:id/visible_removing_fragment_view_tag = 0x7f080172
expo.modules.devclient.test:id/visible = 0x7f080171
expo.modules.devclient.test:id/view_tree_view_model_store_owner = 0x7f080170
expo.modules.devclient.test:id/view_tree_lifecycle_owner = 0x7f08016d
expo.modules.devclient.test:id/view_tag_native_id = 0x7f08016c
expo.modules.devclient.test:id/view_offset_helper = 0x7f08016a
expo.modules.devclient.test:layout/design_layout_tab_text = 0x7f0b0027
expo.modules.devclient.test:id/view_clipped = 0x7f080169
expo.modules.devclient.test:id/unlabeled = 0x7f080165
expo.modules.devclient.test:id/unchecked = 0x7f080163
expo.modules.devclient.test:id/transition_transform = 0x7f080162
expo.modules.devclient.test:id/transition_scene_layoutid_cache = 0x7f080161
expo.modules.devclient.test:id/transition_position = 0x7f080160
expo.modules.devclient.test:id/transition_current_scene = 0x7f08015e
expo.modules.devclient.test:xml/standalone_badge_gravity_top_start = 0x7f110005
expo.modules.devclient.test:id/transform = 0x7f08015c
expo.modules.devclient.test:id/topPanel = 0x7f08015a
expo.modules.devclient.test:id/top = 0x7f080159
expo.modules.devclient.test:id/title_template = 0x7f080158
expo.modules.devclient.test:id/time = 0x7f080155
expo.modules.devclient.test:string/catalyst_loading_from_url = 0x7f0e0035
expo.modules.devclient.test:id/textinput_placeholder = 0x7f080152
expo.modules.devclient.test:id/textinput_helper_text = 0x7f080151
expo.modules.devclient.test:layout/custom_dialog = 0x7f0b0021
expo.modules.devclient.test:id/text_input_start_icon = 0x7f08014e
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f015e
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f003d
expo.modules.devclient.test:id/text_input_end_icon = 0x7f08014d
expo.modules.devclient.test:string/call_notification_screening_text = 0x7f0e0025
expo.modules.devclient.test:id/textStart = 0x7f08014c
expo.modules.devclient.test:id/textSpacerNoTitle = 0x7f08014b
expo.modules.devclient.test:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f00c8
expo.modules.devclient.test:id/textSpacerNoButtons = 0x7f08014a
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f0f01d8
expo.modules.devclient.test:style/TextAppearance.Design.Prefix = 0x7f0f0172
expo.modules.devclient.test:id/text2 = 0x7f080148
expo.modules.devclient.test:integer/mtrl_calendar_header_orientation = 0x7f09000c
expo.modules.devclient.test:id/test_radiobutton_app_button_tint = 0x7f080146
expo.modules.devclient.test:id/test_radiobutton_android_button_tint = 0x7f080145
expo.modules.devclient.test:id/test_checkbox_app_button_tint = 0x7f080144
expo.modules.devclient.test:id/test_checkbox_android_button_tint = 0x7f080143
expo.modules.devclient.test:id/tag_transition_group = 0x7f08013f
expo.modules.devclient.test:id/tag_state_description = 0x7f08013e
expo.modules.devclient.test:id/tag_on_receive_content_listener = 0x7f08013b
expo.modules.devclient.test:styleable/MaterialButton = 0x7f100039
expo.modules.devclient.test:id/tag_accessibility_actions = 0x7f080136
expo.modules.devclient.test:layout/notification_template_part_time = 0x7f0b005a
expo.modules.devclient.test:id/tabMode = 0x7f080135
expo.modules.devclient.test:id/start = 0x7f080131
expo.modules.devclient.test:id/src_over = 0x7f080130
expo.modules.devclient.test:id/src_in = 0x7f08012f
expo.modules.devclient.test:id/split_action_bar = 0x7f08012d
expo.modules.devclient.test:styleable/ShapeAppearance = 0x7f10004f
expo.modules.devclient.test:id/special_effects_controller_view_tag = 0x7f08012c
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f015d
expo.modules.devclient.test:id/snackbar_text = 0x7f080128
expo.modules.devclient.test:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f004e
expo.modules.devclient.test:id/smallLabel = 0x7f080126
expo.modules.devclient.test:id/skipCollapsed = 0x7f080124
expo.modules.devclient.test:id/showTitle = 0x7f080123
expo.modules.devclient.test:id/showHome = 0x7f080122
expo.modules.devclient.test:id/selected = 0x7f08011f
expo.modules.devclient.test:id/search_voice_btn = 0x7f08011d
expo.modules.devclient.test:id/search_src_text = 0x7f08011c
expo.modules.devclient.test:id/search_mag_icon = 0x7f08011a
expo.modules.devclient.test:id/search_edit_frame = 0x7f080118
expo.modules.devclient.test:styleable/ButtonBarLayout = 0x7f100018
expo.modules.devclient.test:id/search_bar = 0x7f080115
expo.modules.devclient.test:style/Theme.AppCompat.Transparent.NoActionBar = 0x7f0f01a0
expo.modules.devclient.test:id/search_badge = 0x7f080114
expo.modules.devclient.test:string/mtrl_picker_confirm = 0x7f0e0066
expo.modules.devclient.test:id/scrollIndicatorUp = 0x7f080111
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Headline1 = 0x7f0f017c
expo.modules.devclient.test:id/scrollIndicatorDown = 0x7f080110
expo.modules.devclient.test:id/screen = 0x7f08010e
expo.modules.devclient.test:id/scale = 0x7f08010d
expo.modules.devclient.test:id/search_plate = 0x7f08011b
expo.modules.devclient.test:id/rounded = 0x7f080109
expo.modules.devclient.test:id/rn_redbox_stack = 0x7f080107
expo.modules.devclient.test:styleable/AnimatedStateListDrawableItem = 0x7f100008
expo.modules.devclient.test:id/rn_redbox_report_label = 0x7f080106
expo.modules.devclient.test:id/rn_redbox_report_button = 0x7f080105
expo.modules.devclient.test:id/rn_redbox_reload_button = 0x7f080104
expo.modules.devclient.test:string/chip_text = 0x7f0e0043
expo.modules.devclient.test:id/rn_redbox_line_separator = 0x7f080102
expo.modules.devclient.test:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0242
expo.modules.devclient.test:id/rn_frame_method = 0x7f080100
expo.modules.devclient.test:id/rn_frame_file = 0x7f0800ff
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light = 0x7f0f0050
expo.modules.devclient.test:id/right_side = 0x7f0800fe
expo.modules.devclient.test:id/right = 0x7f0800fc
expo.modules.devclient.test:id/report_drawn = 0x7f0800fb
expo.modules.devclient.test:id/reloadButton = 0x7f0800fa
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f0026
expo.modules.devclient.test:id/react_test_id = 0x7f0800f9
expo.modules.devclient.test:id/radio = 0x7f0800f8
expo.modules.devclient.test:id/progress_horizontal = 0x7f0800f7
expo.modules.devclient.test:id/pointer_events = 0x7f0800f5
expo.modules.devclient.test:id/parent_matrix = 0x7f0800f1
expo.modules.devclient.test:id/parentPanel = 0x7f0800f0
expo.modules.devclient.test:id/outline = 0x7f0800ee
expo.modules.devclient.test:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f0113
expo.modules.devclient.test:id/on = 0x7f0800ed
expo.modules.devclient.test:id/off = 0x7f0800ec
expo.modules.devclient.test:id/parallax = 0x7f0800ef
expo.modules.devclient.test:id/notification_main_column_container = 0x7f0800eb
expo.modules.devclient.test:id/notification_main_column = 0x7f0800ea
expo.modules.devclient.test:id/noScroll = 0x7f0800e6
expo.modules.devclient.test:id/navigation_header_container = 0x7f0800e4
expo.modules.devclient.test:id/multiply = 0x7f0800e3
expo.modules.devclient.test:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f0112
expo.modules.devclient.test:id/mtrl_picker_title_text = 0x7f0800e2
expo.modules.devclient.test:id/mtrl_picker_text_input_range_end = 0x7f0800e0
expo.modules.devclient.test:id/mtrl_picker_text_input_date = 0x7f0800df
expo.modules.devclient.test:id/mtrl_picker_header_selection_text = 0x7f0800dc
expo.modules.devclient.test:id/mtrl_picker_fullscreen = 0x7f0800da
expo.modules.devclient.test:id/mtrl_card_checked_layer_id = 0x7f0800d6
expo.modules.devclient.test:id/mtrl_calendar_year_selector_frame = 0x7f0800d5
expo.modules.devclient.test:id/mtrl_calendar_text_input_frame = 0x7f0800d4
expo.modules.devclient.test:layout/mtrl_calendar_vertical = 0x7f0b0047
expo.modules.devclient.test:id/mtrl_calendar_selection_frame = 0x7f0800d3
expo.modules.devclient.test:id/mtrl_calendar_main_pane = 0x7f0800d1
expo.modules.devclient.test:id/mtrl_calendar_day_selector_frame = 0x7f0800ce
expo.modules.devclient.test:id/month_title = 0x7f0800cd
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f0070
expo.modules.devclient.test:id/month_navigation_fragment_toggle = 0x7f0800ca
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f0f0207
expo.modules.devclient.test:id/month_navigation_bar = 0x7f0800c9
expo.modules.devclient.test:id/middle = 0x7f0800c5
expo.modules.devclient.test:style/Widget.AppCompat.SeekBar = 0x7f0f0255
expo.modules.devclient.test:id/masked = 0x7f0800c3
expo.modules.devclient.test:id/main_layout = 0x7f0800c2
expo.modules.devclient.test:id/list_view = 0x7f0800c1
expo.modules.devclient.test:id/list_item = 0x7f0800c0
expo.modules.devclient.test:id/line3 = 0x7f0800be
expo.modules.devclient.test:id/select_dialog_listview = 0x7f08011e
expo.modules.devclient.test:id/line1 = 0x7f0800bd
expo.modules.devclient.test:id/largeLabel = 0x7f0800bb
expo.modules.devclient.test:id/labeled = 0x7f0800b9
expo.modules.devclient.test:id/mtrl_internal_children_alpha_tag = 0x7f0800d8
expo.modules.devclient.test:id/item_touch_helper_previous_elevation = 0x7f0800b8
expo.modules.devclient.test:id/info = 0x7f0800b5
expo.modules.devclient.test:id/image = 0x7f0800b4
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f0f01b7
expo.modules.devclient.test:id/ifRoom = 0x7f0800b3
expo.modules.devclient.test:id/icon = 0x7f0800b1
expo.modules.devclient.test:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f0f0263
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionButton = 0x7f0f0239
expo.modules.devclient.test:id/homeButton = 0x7f0800b0
expo.modules.devclient.test:styleable/ViewPager2 = 0x7f100065
expo.modules.devclient.test:id/homeAsUp = 0x7f0800af
expo.modules.devclient.test:id/home = 0x7f0800ae
expo.modules.devclient.test:id/hideable = 0x7f0800ad
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f0f02ca
expo.modules.devclient.test:id/hide_ime_id = 0x7f0800ac
expo.modules.devclient.test:id/group_divider = 0x7f0800ab
expo.modules.devclient.test:style/Widget.AppCompat.PopupMenu = 0x7f0f024b
expo.modules.devclient.test:id/gone = 0x7f0800aa
expo.modules.devclient.test:id/tag_window_insets_animation_callback = 0x7f080142
expo.modules.devclient.test:id/ghost_view_holder = 0x7f0800a9
expo.modules.devclient.test:id/ghost_view = 0x7f0800a8
expo.modules.devclient.test:id/fragment_container_view_tag = 0x7f0800a7
expo.modules.devclient.test:id/fps_text = 0x7f0800a6
expo.modules.devclient.test:id/focusCrop = 0x7f0800a4
expo.modules.devclient.test:id/floating = 0x7f0800a3
expo.modules.devclient.test:id/fixed = 0x7f0800a2
expo.modules.devclient.test:id/fitXY = 0x7f0800a1
expo.modules.devclient.test:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f0f0274
expo.modules.devclient.test:id/fitStart = 0x7f08009f
expo.modules.devclient.test:style/Platform.Widget.AppCompat.Spinner = 0x7f0f0102
expo.modules.devclient.test:id/fitEnd = 0x7f08009e
expo.modules.devclient.test:id/fitCenter = 0x7f08009d
expo.modules.devclient.test:id/fill_horizontal = 0x7f080098
expo.modules.devclient.test:id/fill = 0x7f080097
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f0f01f9
expo.modules.devclient.test:id/fade = 0x7f080096
expo.modules.devclient.test:id/expanded_menu = 0x7f080095
expo.modules.devclient.test:id/error_viewPager = 0x7f080092
expo.modules.devclient.test:id/error_title = 0x7f080091
expo.modules.devclient.test:id/error_stack = 0x7f080090
expo.modules.devclient.test:id/error_main_content = 0x7f08008f
expo.modules.devclient.test:style/TextAppearance.AppCompat.Display3 = 0x7f0f013d
expo.modules.devclient.test:id/error_footer_content = 0x7f08008e
expo.modules.devclient.test:id/error_footer = 0x7f08008d
expo.modules.devclient.test:id/errorDetails = 0x7f08008c
expo.modules.devclient.test:id/enterAlwaysCollapsed = 0x7f08008b
expo.modules.devclient.test:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f100026
expo.modules.devclient.test:id/scroll = 0x7f08010f
expo.modules.devclient.test:id/notification_background = 0x7f0800e9
expo.modules.devclient.test:id/enterAlways = 0x7f08008a
expo.modules.devclient.test:id/end = 0x7f080089
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f0f02c8
expo.modules.devclient.test:id/dropdown_menu = 0x7f080086
expo.modules.devclient.test:id/disableHome = 0x7f080085
expo.modules.devclient.test:string/catalyst_hot_reloading_stop = 0x7f0e0033
expo.modules.devclient.test:id/dialog_button = 0x7f080084
expo.modules.devclient.test:style/ShapeAppearanceOverlay.TopLeftCut = 0x7f0f0127
expo.modules.devclient.test:id/design_navigation_view = 0x7f080083
expo.modules.devclient.test:id/design_bottom_sheet = 0x7f08007f
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Badge = 0x7f0f0176
expo.modules.devclient.test:id/default_activity_button = 0x7f08007e
expo.modules.devclient.test:layout/design_layout_snackbar_include = 0x7f0b0025
expo.modules.devclient.test:id/decor_content_parent = 0x7f08007d
expo.modules.devclient.test:id/submit_area = 0x7f080134
expo.modules.devclient.test:id/date_picker_actions = 0x7f08007c
expo.modules.devclient.test:id/cut = 0x7f08007b
expo.modules.devclient.test:string/abc_searchview_description_clear = 0x7f0e0013
expo.modules.devclient.test:id/coordinator = 0x7f080078
expo.modules.devclient.test:id/contentPanel = 0x7f080077
expo.modules.devclient.test:id/content = 0x7f080076
expo.modules.devclient.test:id/container = 0x7f080075
expo.modules.devclient.test:id/console_reload_button = 0x7f080074
expo.modules.devclient.test:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f0083
expo.modules.devclient.test:id/console_home_button = 0x7f080073
expo.modules.devclient.test:id/clip_horizontal = 0x7f08006f
expo.modules.devclient.test:id/clear_text = 0x7f08006e
expo.modules.devclient.test:id/chip_group = 0x7f08006c
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f0f0078
expo.modules.devclient.test:id/chip3 = 0x7f08006b
expo.modules.devclient.test:id/chip = 0x7f080068
expo.modules.devclient.test:xml/standalone_badge_gravity_bottom_end = 0x7f110003
expo.modules.devclient.test:style/Widget.AppCompat.ListView = 0x7f0f0248
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f0f01b6
expo.modules.devclient.test:id/center_horizontal = 0x7f080064
expo.modules.devclient.test:id/centerInside = 0x7f080063
expo.modules.devclient.test:id/centerCrop = 0x7f080062
expo.modules.devclient.test:string/mtrl_picker_text_input_year_abbr = 0x7f0e007c
expo.modules.devclient.test:id/catalyst_redbox_title = 0x7f080060
expo.modules.devclient.test:id/cancel_button = 0x7f08005f
expo.modules.devclient.test:id/button_text = 0x7f08005e
expo.modules.devclient.test:string/mtrl_picker_range_header_only_end_selected = 0x7f0e0071
expo.modules.devclient.test:id/buttonPanel = 0x7f08005d
expo.modules.devclient.test:id/button = 0x7f08005c
expo.modules.devclient.test:id/checkbox = 0x7f080066
expo.modules.devclient.test:id/browser_actions_menu_view = 0x7f08005b
expo.modules.devclient.test:id/browser_actions_menu_item_text = 0x7f080059
expo.modules.devclient.test:id/browser_actions_menu_item_icon = 0x7f080058
expo.modules.devclient.test:id/browser_actions_header_text = 0x7f080057
expo.modules.devclient.test:id/bottom_sheet = 0x7f080056
expo.modules.devclient.test:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f0f029b
expo.modules.devclient.test:id/bottom = 0x7f080055
expo.modules.devclient.test:id/blocking = 0x7f080054
expo.modules.devclient.test:id/beginning = 0x7f080053
expo.modules.devclient.test:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
expo.modules.devclient.test:id/autofill_inline_suggestion_title = 0x7f080052
expo.modules.devclient.test:id/autofill_inline_suggestion_subtitle = 0x7f080051
expo.modules.devclient.test:id/fitBottomStart = 0x7f08009c
expo.modules.devclient.test:id/autofill_inline_suggestion_end_icon = 0x7f08004f
expo.modules.devclient.test:id/auto = 0x7f08004e
expo.modules.devclient.test:id/always = 0x7f08004c
expo.modules.devclient.test:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0251
expo.modules.devclient.test:integer/mtrl_chip_anim_duration = 0x7f090011
expo.modules.devclient.test:id/alert_title = 0x7f08004a
expo.modules.devclient.test:id/add = 0x7f080048
expo.modules.devclient.test:string/mtrl_picker_date_header_selected = 0x7f0e0067
expo.modules.devclient.test:id/action_text = 0x7f080045
expo.modules.devclient.test:id/action_mode_bar_stub = 0x7f080043
expo.modules.devclient.test:id/action_mode_bar = 0x7f080042
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f002b
expo.modules.devclient.test:id/action_menu_divider = 0x7f080040
expo.modules.devclient.test:styleable/MaterialTextView = 0x7f100042
expo.modules.devclient.test:id/action_image = 0x7f08003f
expo.modules.devclient.test:id/action_divider = 0x7f08003e
expo.modules.devclient.test:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f0103
expo.modules.devclient.test:id/action_container = 0x7f08003c
expo.modules.devclient.test:id/action_bar_title = 0x7f08003b
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f0162
expo.modules.devclient.test:id/action_bar_subtitle = 0x7f08003a
expo.modules.devclient.test:id/action_bar_spinner = 0x7f080039
expo.modules.devclient.test:id/action_bar_activity_content = 0x7f080036
expo.modules.devclient.test:string/exposed_dropdown_menu_content_description = 0x7f0e004c
expo.modules.devclient.test:id/accessibility_value = 0x7f080034
expo.modules.devclient.test:id/accessibility_state_expanded = 0x7f080033
expo.modules.devclient.test:id/accessibility_hint = 0x7f08002e
expo.modules.devclient.test:id/accessibility_custom_action_31 = 0x7f080027
expo.modules.devclient.test:attr/singleSelection = 0x7f03021c
expo.modules.devclient.test:id/accessibility_custom_action_25 = 0x7f080020
expo.modules.devclient.test:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f00b4
expo.modules.devclient.test:id/accessibility_custom_action_20 = 0x7f08001b
expo.modules.devclient.test:layout/abc_action_mode_close_item_material = 0x7f0b0005
expo.modules.devclient.test:id/accessibility_custom_action_13 = 0x7f080013
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Light = 0x7f0f0200
expo.modules.devclient.test:id/accessibility_custom_action_11 = 0x7f080011
expo.modules.devclient.test:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f0f02bb
expo.modules.devclient.test:color/browser_actions_divider_color = 0x7f050028
expo.modules.devclient.test:id/accessibility_collection_item = 0x7f08000d
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f0027
expo.modules.devclient.test:attr/labelStyle = 0x7f03017c
expo.modules.devclient.test:dimen/mtrl_high_ripple_hovered_alpha = 0x7f060116
expo.modules.devclient.test:id/accessibility_collection = 0x7f08000c
expo.modules.devclient.test:layout/abc_select_dialog_material = 0x7f0b001a
expo.modules.devclient.test:id/accessibility_actions = 0x7f08000b
expo.modules.devclient.test:id/TOP_END = 0x7f080008
expo.modules.devclient.test:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f0146
expo.modules.devclient.test:drawable/tooltip_frame_light = 0x7f0700d6
expo.modules.devclient.test:drawable/test_level_drawable = 0x7f0700d4
expo.modules.devclient.test:id/fill_vertical = 0x7f080099
expo.modules.devclient.test:drawable/redbox_top_border_background = 0x7f0700d1
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f0f005e
expo.modules.devclient.test:id/none = 0x7f0800e7
expo.modules.devclient.test:attr/retryImage = 0x7f0301f2
expo.modules.devclient.test:dimen/mtrl_calendar_header_divider_thickness = 0x7f0600d8
expo.modules.devclient.test:drawable/paused_in_debugger_background = 0x7f0700cf
expo.modules.devclient.test:string/abc_action_bar_up_description = 0x7f0e0001
expo.modules.devclient.test:drawable/notification_template_icon_low_bg = 0x7f0700cc
expo.modules.devclient.test:layout/abc_action_bar_title_item = 0x7f0b0000
expo.modules.devclient.test:drawable/notification_oversize_large_icon_bg = 0x7f0700ca
expo.modules.devclient.test:drawable/abc_action_bar_item_background_material = 0x7f070045
expo.modules.devclient.test:drawable/notification_bg_low_pressed = 0x7f0700c6
expo.modules.devclient.test:drawable/notification_bg_low_normal = 0x7f0700c5
expo.modules.devclient.test:drawable/notification_bg = 0x7f0700c3
expo.modules.devclient.test:drawable/mtrl_ic_cancel = 0x7f0700bc
expo.modules.devclient.test:style/Widget.Design.NavigationView = 0x7f0f026c
expo.modules.devclient.test:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
expo.modules.devclient.test:drawable/mtrl_ic_arrow_drop_down = 0x7f0700ba
expo.modules.devclient.test:drawable/mtrl_dropdown_arrow = 0x7f0700b9
expo.modules.devclient.test:id/shortcut = 0x7f080120
expo.modules.devclient.test:dimen/material_emphasis_disabled = 0x7f06009e
expo.modules.devclient.test:drawable/mtrl_dialog_background = 0x7f0700b8
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f0029
expo.modules.devclient.test:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700b6
expo.modules.devclient.test:attr/popupMenuStyle = 0x7f0301db
expo.modules.devclient.test:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700b5
expo.modules.devclient.test:id/mtrl_child_content_container = 0x7f0800d7
expo.modules.devclient.test:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700b4
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0040
expo.modules.devclient.test:drawable/material_ic_edit_black_24dp = 0x7f0700b3
expo.modules.devclient.test:drawable/ic_resume = 0x7f0700b0
expo.modules.devclient.test:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f024f
expo.modules.devclient.test:drawable/mtrl_ic_arrow_drop_up = 0x7f0700bb
expo.modules.devclient.test:drawable/ic_mtrl_chip_close_circle = 0x7f0700af
expo.modules.devclient.test:drawable/ic_mtrl_chip_checked_black = 0x7f0700ad
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f0f028f
expo.modules.devclient.test:drawable/ic_call_decline = 0x7f0700aa
expo.modules.devclient.test:drawable/ic_call_answer_low = 0x7f0700a7
expo.modules.devclient.test:drawable/dev_launcher_ic_refresh_white_36dp = 0x7f0700a5
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f0036
expo.modules.devclient.test:color/material_on_primary_emphasis_medium = 0x7f05007f
expo.modules.devclient.test:drawable/design_password_eye = 0x7f0700a2
expo.modules.devclient.test:drawable/design_bottom_navigation_item_background = 0x7f07009e
expo.modules.devclient.test:attr/itemHorizontalPadding = 0x7f030163
expo.modules.devclient.test:drawable/btn_checkbox_unchecked_mtrl = 0x7f070098
expo.modules.devclient.test:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070097
expo.modules.devclient.test:drawable/avd_show_password = 0x7f070095
expo.modules.devclient.test:drawable/abc_vector_test = 0x7f070092
expo.modules.devclient.test:drawable/abc_seekbar_track_material = 0x7f070080
expo.modules.devclient.test:id/accessibility_action_clickable_span = 0x7f08000a
expo.modules.devclient.test:attr/materialCalendarHeaderSelection = 0x7f0301ad
expo.modules.devclient.test:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070090
expo.modules.devclient.test:drawable/btn_radio_off_mtrl = 0x7f07009a
expo.modules.devclient.test:dimen/design_bottom_navigation_margin = 0x7f06006a
expo.modules.devclient.test:drawable/abc_textfield_default_mtrl_alpha = 0x7f07008e
expo.modules.devclient.test:style/Widget.AppCompat.SearchView = 0x7f0f0253
expo.modules.devclient.test:drawable/abc_text_select_handle_middle_mtrl = 0x7f07008b
expo.modules.devclient.test:drawable/abc_text_select_handle_left_mtrl = 0x7f07008a
expo.modules.devclient.test:id/chip1 = 0x7f080069
expo.modules.devclient.test:drawable/abc_tab_indicator_material = 0x7f070087
expo.modules.devclient.test:drawable/abc_switch_track_mtrl_alpha = 0x7f070086
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_toolbaroverlayicon = 0x7f07003b
expo.modules.devclient.test:id/accessibility_label = 0x7f08002f
expo.modules.devclient.test:layout/mtrl_picker_header_title_text = 0x7f0b0051
expo.modules.devclient.test:drawable/abc_item_background_holo_light = 0x7f070067
expo.modules.devclient.test:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700b7
expo.modules.devclient.test:drawable/abc_spinner_textfield_background_material = 0x7f070082
expo.modules.devclient.test:drawable/abc_seekbar_tick_mark_material = 0x7f07007f
expo.modules.devclient.test:layout/design_menu_item_action_area = 0x7f0b0028
expo.modules.devclient.test:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009d
expo.modules.devclient.test:attr/fastScrollEnabled = 0x7f030127
expo.modules.devclient.test:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f07007c
expo.modules.devclient.test:attr/itemFillColor = 0x7f030162
expo.modules.devclient.test:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07007b
expo.modules.devclient.test:styleable/Toolbar = 0x7f100061
expo.modules.devclient.test:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070079
expo.modules.devclient.test:drawable/abc_ratingbar_small_material = 0x7f070078
expo.modules.devclient.test:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f07005c
expo.modules.devclient.test:drawable/abc_ratingbar_indicator_material = 0x7f070076
expo.modules.devclient.test:attr/drawableStartCompat = 0x7f0300f4
expo.modules.devclient.test:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070074
expo.modules.devclient.test:drawable/abc_list_selector_background_transition_holo_light = 0x7f07006f
expo.modules.devclient.test:color/abc_search_url_text_selected = 0x7f050010
expo.modules.devclient.test:color/mtrl_navigation_item_text_color = 0x7f0500ac
expo.modules.devclient.test:drawable/abc_list_pressed_holo_dark = 0x7f07006c
expo.modules.devclient.test:layout/text_view_with_line_height_from_layout = 0x7f0b006d
expo.modules.devclient.test:drawable/abc_list_focused_holo = 0x7f07006a
expo.modules.devclient.test:drawable/abc_list_divider_mtrl_alpha = 0x7f070069
expo.modules.devclient.test:drawable/abc_item_background_holo_dark = 0x7f070066
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_extensionsfilledinactiveiconlight = 0x7f070020
expo.modules.devclient.test:drawable/abc_ic_search_api_material = 0x7f070064
expo.modules.devclient.test:id/textinput_counter = 0x7f08014f
expo.modules.devclient.test:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
expo.modules.devclient.test:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070062
expo.modules.devclient.test:drawable/abc_ic_menu_overflow_material = 0x7f070060
expo.modules.devclient.test:anim/catalyst_push_up_in = 0x7f01001a
expo.modules.devclient.test:attr/textAppearanceHeadline5 = 0x7f03026a
expo.modules.devclient.test:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07005f
expo.modules.devclient.test:attr/actionBarSize = 0x7f030003
expo.modules.devclient.test:dimen/mtrl_slider_label_radius = 0x7f060129
expo.modules.devclient.test:drawable/design_snackbar_background = 0x7f0700a3
expo.modules.devclient.test:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07005e
expo.modules.devclient.test:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_chevronleft = 0x7f070011
expo.modules.devclient.test:drawable/abc_list_selector_disabled_holo_dark = 0x7f070070
expo.modules.devclient.test:id/accessibility_custom_action_14 = 0x7f080014
expo.modules.devclient.test:dimen/mtrl_calendar_header_content_padding = 0x7f0600d6
expo.modules.devclient.test:drawable/abc_ic_clear_material = 0x7f07005b
expo.modules.devclient.test:attr/startIconDrawable = 0x7f03022a
expo.modules.devclient.test:drawable/abc_ic_ab_back_material = 0x7f070059
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f0f0280
expo.modules.devclient.test:drawable/abc_dialog_material_background = 0x7f070057
expo.modules.devclient.test:attr/actionModeShareDrawable = 0x7f03001a
expo.modules.devclient.test:attr/fontVariationSettings = 0x7f030138
expo.modules.devclient.test:drawable/abc_control_background_material = 0x7f070056
expo.modules.devclient.test:dimen/mtrl_tooltip_arrowSize = 0x7f060140
expo.modules.devclient.test:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070055
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f023b
expo.modules.devclient.test:drawable/abc_cab_background_internal_bg = 0x7f070053
expo.modules.devclient.test:style/Base.CardView = 0x7f0f000f
expo.modules.devclient.test:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070050
expo.modules.devclient.test:dimen/mtrl_extended_fab_end_padding = 0x7f060104
expo.modules.devclient.test:drawable/abc_btn_radio_material_anim = 0x7f07004e
expo.modules.devclient.test:drawable/abc_btn_default_mtrl_shape = 0x7f07004c
expo.modules.devclient.test:drawable/abc_btn_colored_material = 0x7f07004b
expo.modules.devclient.test:drawable/abc_btn_check_material = 0x7f070047
expo.modules.devclient.test:drawable/abc_btn_borderless_material = 0x7f070046
expo.modules.devclient.test:id/accessibility_custom_action_7 = 0x7f08002b
expo.modules.devclient.test:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f023e
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f01e5
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_xiconlight = 0x7f070043
expo.modules.devclient.test:attr/subtitleTextColor = 0x7f03023c
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_xicon = 0x7f070042
expo.modules.devclient.test:attr/isMaterialTheme = 0x7f030160
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_updateiconlight = 0x7f07003d
expo.modules.devclient.test:string/abc_capital_off = 0x7f0e0006
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_updateicon = 0x7f07003c
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f0f01c6
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_threefingerlongpressiconlight = 0x7f07003a
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_settingsfilledactiveiconlight = 0x7f070031
expo.modules.devclient.test:animator/mtrl_chip_state_list_anim = 0x7f02000c
expo.modules.devclient.test:color/mtrl_bottom_nav_colored_item_tint = 0x7f05008a
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_settingsfilledactiveicon = 0x7f070030
expo.modules.devclient.test:attr/passwordToggleEnabled = 0x7f0301d2
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_runicon = 0x7f07002f
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_refreshicon = 0x7f07002e
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_performanceicon = 0x7f07002c
expo.modules.devclient.test:attr/colorControlNormal = 0x7f0300b0
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_logoicon = 0x7f07002b
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_loadingindicatoricon = 0x7f07002a
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_inspectelementicon = 0x7f070029
expo.modules.devclient.test:style/TextAppearance.AppCompat.Menu = 0x7f0f0149
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_homefilledinactiveicon = 0x7f070025
expo.modules.devclient.test:attr/backgroundColor = 0x7f03003f
expo.modules.devclient.test:color/browser_actions_title_color = 0x7f05002a
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_extensionsiconlight = 0x7f070022
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_extensionsicon = 0x7f070021
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_extensionsfilledactiveiconlight = 0x7f07001e
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_clipboardicon = 0x7f07001b
expo.modules.devclient.test:xml/standalone_badge_offset = 0x7f110006
expo.modules.devclient.test:color/design_dark_default_color_secondary_variant = 0x7f050043
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_chevronrighticon = 0x7f07001a
expo.modules.devclient.test:drawable/notification_action_background = 0x7f0700c2
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_branchiconlight = 0x7f070016
expo.modules.devclient.test:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f018d
expo.modules.devclient.test:attr/textAppearanceHeadline6 = 0x7f03026b
expo.modules.devclient.test:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07006e
expo.modules.devclient.test:dimen/notification_top_pad_large_text = 0x7f060154
expo.modules.devclient.test:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_loader = 0x7f070014
expo.modules.devclient.test:attr/titleTextAppearance = 0x7f030298
expo.modules.devclient.test:drawable/notification_bg_normal = 0x7f0700c7
expo.modules.devclient.test:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_chevronright = 0x7f070012
expo.modules.devclient.test:color/design_default_color_on_primary = 0x7f050049
expo.modules.devclient.test:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_alerttriangle = 0x7f070010
expo.modules.devclient.test:drawable/__node_modules_reactnavigation_elements_lib_module_assets_backicon = 0x7f07000b
expo.modules.devclient.test:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_questionmarkicon = 0x7f07002d
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f0f0066
expo.modules.devclient.test:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_chevronleft = 0x7f070007
expo.modules.devclient.test:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f019b
expo.modules.devclient.test:string/search_menu_title = 0x7f0e008a
expo.modules.devclient.test:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_alerttriangle = 0x7f070006
expo.modules.devclient.test:drawable/$avd_show_password__2 = 0x7f070005
expo.modules.devclient.test:id/accessibility_state = 0x7f080032
expo.modules.devclient.test:drawable/$avd_show_password__1 = 0x7f070004
expo.modules.devclient.test:drawable/$avd_hide_password__2 = 0x7f070002
expo.modules.devclient.test:drawable/$avd_hide_password__0 = 0x7f070000
expo.modules.devclient.test:dimen/tooltip_vertical_padding = 0x7f06015b
expo.modules.devclient.test:dimen/tooltip_precise_anchor_extra_offset = 0x7f060159
expo.modules.devclient.test:drawable/dev_laucher_ic_home_white_36dp = 0x7f0700a4
expo.modules.devclient.test:dimen/notification_top_pad = 0x7f060153
expo.modules.devclient.test:attr/behavior_autoHide = 0x7f03004e
expo.modules.devclient.test:drawable/notify_panel_notification_icon_bg = 0x7f0700ce
expo.modules.devclient.test:dimen/notification_right_side_padding_top = 0x7f06014f
expo.modules.devclient.test:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f0f02bf
expo.modules.devclient.test:string/tablist_description = 0x7f0e0096
expo.modules.devclient.test:id/fitToContents = 0x7f0800a0
expo.modules.devclient.test:dimen/notification_media_narrow_margin = 0x7f06014d
expo.modules.devclient.test:string/catalyst_copy_button = 0x7f0e0027
expo.modules.devclient.test:dimen/notification_action_icon_size = 0x7f060146
expo.modules.devclient.test:dimen/mtrl_tooltip_minWidth = 0x7f060143
expo.modules.devclient.test:dimen/mtrl_calendar_day_corner = 0x7f0600ce
expo.modules.devclient.test:id/META = 0x7f080005
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_debugicon = 0x7f07001c
expo.modules.devclient.test:color/mtrl_choice_chip_background_color = 0x7f05009f
expo.modules.devclient.test:dimen/mtrl_toolbar_default_height = 0x7f06013f
expo.modules.devclient.test:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f06013d
expo.modules.devclient.test:styleable/MaterialTextAppearance = 0x7f100041
expo.modules.devclient.test:attr/yearSelectedStyle = 0x7f0302bc
expo.modules.devclient.test:dimen/mtrl_textinput_end_icon_margin_start = 0x7f06013c
expo.modules.devclient.test:attr/trackColorActive = 0x7f0302a4
expo.modules.devclient.test:drawable/ic_mtrl_checked_circle = 0x7f0700ac
expo.modules.devclient.test:id/showCustom = 0x7f080121
expo.modules.devclient.test:attr/actionModeTheme = 0x7f03001d
expo.modules.devclient.test:dimen/mtrl_textinput_counter_margin_start = 0x7f06013b
expo.modules.devclient.test:drawable/abc_ic_go_search_api_material = 0x7f07005d
expo.modules.devclient.test:dimen/mtrl_textinput_box_corner_radius_small = 0x7f060137
expo.modules.devclient.test:dimen/mtrl_textinput_start_icon_margin_end = 0x7f06013e
expo.modules.devclient.test:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
expo.modules.devclient.test:dimen/mtrl_snackbar_margin = 0x7f060134
expo.modules.devclient.test:animator/fragment_open_exit = 0x7f020008
expo.modules.devclient.test:dimen/mtrl_snackbar_background_corner_radius = 0x7f060132
expo.modules.devclient.test:id/TOP_START = 0x7f080009
expo.modules.devclient.test:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f060131
expo.modules.devclient.test:color/mtrl_chip_text_color = 0x7f05009e
expo.modules.devclient.test:dimen/mtrl_slider_track_height = 0x7f06012d
expo.modules.devclient.test:attr/enforceMaterialTheme = 0x7f030108
expo.modules.devclient.test:dimen/mtrl_slider_thumb_radius = 0x7f06012c
expo.modules.devclient.test:dimen/mtrl_slider_label_square_side = 0x7f06012a
expo.modules.devclient.test:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f060123
expo.modules.devclient.test:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f060122
expo.modules.devclient.test:dimen/mtrl_navigation_item_icon_size = 0x7f060121
expo.modules.devclient.test:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
expo.modules.devclient.test:dimen/mtrl_low_ripple_hovered_alpha = 0x7f06011b
expo.modules.devclient.test:dimen/mtrl_low_ripple_focused_alpha = 0x7f06011a
expo.modules.devclient.test:style/Base.Animation.AppCompat.Dialog = 0x7f0f000c
expo.modules.devclient.test:dimen/mtrl_large_touch_target = 0x7f060118
expo.modules.devclient.test:id/center = 0x7f080061
expo.modules.devclient.test:id/accessibility_role = 0x7f080031
expo.modules.devclient.test:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f060112
expo.modules.devclient.test:attr/singleChoiceItemLayout = 0x7f03021a
expo.modules.devclient.test:dimen/design_navigation_elevation = 0x7f060077
expo.modules.devclient.test:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f06010f
expo.modules.devclient.test:color/dev_launcher_primary = 0x7f050061
expo.modules.devclient.test:dimen/mtrl_extended_fab_start_padding = 0x7f06010a
expo.modules.devclient.test:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
expo.modules.devclient.test:dimen/mtrl_extended_fab_min_height = 0x7f060108
expo.modules.devclient.test:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f060107
expo.modules.devclient.test:dimen/mtrl_extended_fab_icon_size = 0x7f060106
expo.modules.devclient.test:dimen/design_navigation_item_icon_padding = 0x7f06007b
expo.modules.devclient.test:drawable/__reactnativelab_reactnative_libraries_logbox_ui_logboximages_close = 0x7f070013
expo.modules.devclient.test:drawable/abc_textfield_search_material = 0x7f070091
expo.modules.devclient.test:attr/windowFixedWidthMajor = 0x7f0302b7
expo.modules.devclient.test:drawable/abc_btn_check_material_anim = 0x7f070048
expo.modules.devclient.test:id/spacer = 0x7f08012b
expo.modules.devclient.test:dimen/mtrl_extended_fab_elevation = 0x7f060103
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f00bd
expo.modules.devclient.test:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f060102
expo.modules.devclient.test:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0600fd
expo.modules.devclient.test:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0600fc
expo.modules.devclient.test:dimen/mtrl_edittext_rectangle_top_offset = 0x7f0600fb
expo.modules.devclient.test:attr/autoSizeMaxTextSize = 0x7f030034
expo.modules.devclient.test:dimen/mtrl_card_dragged_z = 0x7f0600f6
expo.modules.devclient.test:attr/isLightTheme = 0x7f03015f
expo.modules.devclient.test:drawable/__node_modules_reactnavigation_elements_lib_module_assets_searchicon = 0x7f07000f
expo.modules.devclient.test:layout/mtrl_picker_text_input_date = 0x7f0b0053
expo.modules.devclient.test:dimen/mtrl_card_checked_icon_size = 0x7f0600f4
expo.modules.devclient.test:id/accessibility_custom_action_29 = 0x7f080024
expo.modules.devclient.test:dimen/mtrl_calendar_year_height = 0x7f0600ef
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f0033
expo.modules.devclient.test:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0600ec
expo.modules.devclient.test:attr/buttonBarNegativeButtonStyle = 0x7f03006b
expo.modules.devclient.test:dimen/mtrl_calendar_text_input_padding_top = 0x7f0600eb
expo.modules.devclient.test:styleable/ActivityChooserView = 0x7f100005
expo.modules.devclient.test:color/dev_launcher_backgroundColor = 0x7f05005c
expo.modules.devclient.test:id/accessibility_custom_action_10 = 0x7f080010
expo.modules.devclient.test:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f07009d
expo.modules.devclient.test:drawable/__node_modules_reactnavigation_elements_lib_module_assets_closeicon = 0x7f07000e
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f0f005b
expo.modules.devclient.test:id/progress_circular = 0x7f0800f6
expo.modules.devclient.test:attr/errorIconTintMode = 0x7f03010f
expo.modules.devclient.test:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0600e9
expo.modules.devclient.test:string/abc_action_menu_overflow_description = 0x7f0e0002
expo.modules.devclient.test:attr/backgroundTintMode = 0x7f030049
expo.modules.devclient.test:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0600e8
expo.modules.devclient.test:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b003d
expo.modules.devclient.test:id/action_bar_root = 0x7f080038
expo.modules.devclient.test:color/test_mtrl_calendar_day = 0x7f0500d4
expo.modules.devclient.test:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0600e0
expo.modules.devclient.test:style/Theme.AppCompat.CompactMenu = 0x7f0f018b
expo.modules.devclient.test:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f0f0012
expo.modules.devclient.test:dimen/mtrl_calendar_landscape_header_width = 0x7f0600df
expo.modules.devclient.test:drawable/material_ic_clear_black_24dp = 0x7f0700b2
expo.modules.devclient.test:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f0f0116
expo.modules.devclient.test:string/mtrl_picker_toggle_to_day_selection = 0x7f0e007e
expo.modules.devclient.test:dimen/mtrl_calendar_header_text_padding = 0x7f0600dc
expo.modules.devclient.test:dimen/mtrl_calendar_header_height = 0x7f0600d9
expo.modules.devclient.test:dimen/mtrl_shape_corner_size_small_component = 0x7f060126
expo.modules.devclient.test:dimen/mtrl_calendar_days_of_week_height = 0x7f0600d4
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_homefilledactiveiconlight = 0x7f070024
expo.modules.devclient.test:dimen/mtrl_calendar_day_width = 0x7f0600d3
expo.modules.devclient.test:attr/titleEnabled = 0x7f030291
expo.modules.devclient.test:attr/scrimBackground = 0x7f030205
expo.modules.devclient.test:dimen/mtrl_calendar_day_vertical_padding = 0x7f0600d2
expo.modules.devclient.test:drawable/abc_popup_background_mtrl_mult = 0x7f070075
expo.modules.devclient.test:dimen/mtrl_calendar_day_today_stroke = 0x7f0600d1
expo.modules.devclient.test:dimen/mtrl_card_corner_radius = 0x7f0600f5
expo.modules.devclient.test:dimen/mtrl_calendar_day_horizontal_padding = 0x7f0600d0
expo.modules.devclient.test:dimen/mtrl_calendar_day_height = 0x7f0600cf
expo.modules.devclient.test:dimen/hint_alpha_material_dark = 0x7f060097
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_settingsfilledinactiveiconlight = 0x7f070033
expo.modules.devclient.test:dimen/mtrl_calendar_content_padding = 0x7f0600cd
expo.modules.devclient.test:dimen/mtrl_calendar_action_height = 0x7f0600ca
expo.modules.devclient.test:dimen/notification_small_icon_size_as_large = 0x7f060151
expo.modules.devclient.test:id/accessibility_links = 0x7f080030
expo.modules.devclient.test:dimen/mtrl_btn_z = 0x7f0600c9
expo.modules.devclient.test:id/useLogo = 0x7f080167
expo.modules.devclient.test:dimen/notification_small_icon_background_padding = 0x7f060150
expo.modules.devclient.test:id/alertTitle = 0x7f080049
expo.modules.devclient.test:drawable/abc_textfield_activated_mtrl_alpha = 0x7f07008d
expo.modules.devclient.test:dimen/mtrl_btn_text_btn_padding_right = 0x7f0600c7
expo.modules.devclient.test:attr/isAutofillInlineSuggestionTheme = 0x7f03015e
expo.modules.devclient.test:dimen/mtrl_btn_text_btn_icon_padding = 0x7f0600c5
expo.modules.devclient.test:styleable/LinearLayoutCompat = 0x7f100033
expo.modules.devclient.test:dimen/mtrl_btn_stroke_size = 0x7f0600c4
expo.modules.devclient.test:id/mtrl_picker_header = 0x7f0800db
expo.modules.devclient.test:color/mtrl_textinput_focused_box_stroke_color = 0x7f0500bb
expo.modules.devclient.test:dimen/mtrl_btn_padding_top = 0x7f0600c2
expo.modules.devclient.test:color/call_notification_decline_color = 0x7f05002e
expo.modules.devclient.test:dimen/mtrl_btn_padding_left = 0x7f0600c0
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f0f0204
expo.modules.devclient.test:dimen/highlight_alpha_material_colored = 0x7f060094
expo.modules.devclient.test:dimen/mtrl_btn_text_btn_padding_left = 0x7f0600c6
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f0023
expo.modules.devclient.test:dimen/mtrl_btn_padding_bottom = 0x7f0600bf
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f023d
expo.modules.devclient.test:id/src_atop = 0x7f08012e
expo.modules.devclient.test:dimen/mtrl_btn_inset = 0x7f0600bd
expo.modules.devclient.test:attr/searchViewStyle = 0x7f030209
expo.modules.devclient.test:dimen/mtrl_btn_focused_z = 0x7f0600b9
expo.modules.devclient.test:drawable/abc_cab_background_top_material = 0x7f070054
expo.modules.devclient.test:dimen/mtrl_btn_disabled_z = 0x7f0600b7
expo.modules.devclient.test:color/call_notification_answer_color = 0x7f05002d
expo.modules.devclient.test:dimen/mtrl_btn_disabled_elevation = 0x7f0600b6
expo.modules.devclient.test:dimen/mtrl_btn_dialog_btn_min_width = 0x7f0600b5
expo.modules.devclient.test:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600b1
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f01ea
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f0109
expo.modules.devclient.test:id/browser_actions_menu_items = 0x7f08005a
expo.modules.devclient.test:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f0600af
expo.modules.devclient.test:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f0600ae
expo.modules.devclient.test:id/accessibility_custom_action_6 = 0x7f08002a
expo.modules.devclient.test:dimen/mtrl_badge_with_text_radius = 0x7f0600ad
expo.modules.devclient.test:dimen/tooltip_horizontal_padding = 0x7f060157
expo.modules.devclient.test:attr/chipStyle = 0x7f03009c
expo.modules.devclient.test:attr/passwordToggleContentDescription = 0x7f0301d0
expo.modules.devclient.test:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f0600a9
expo.modules.devclient.test:style/Theme.AppCompat.Empty = 0x7f0f0197
expo.modules.devclient.test:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0600ed
expo.modules.devclient.test:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f00b0
expo.modules.devclient.test:attr/verticalOffset = 0x7f0302ae
expo.modules.devclient.test:dimen/mtrl_badge_horizontal_edge_offset = 0x7f0600a8
expo.modules.devclient.test:id/tag_accessibility_clickable_spans = 0x7f080137
expo.modules.devclient.test:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f0600a7
expo.modules.devclient.test:style/WhiteBackgroundDialogTheme = 0x7f0f0214
expo.modules.devclient.test:id/snackbar_action = 0x7f080127
expo.modules.devclient.test:dimen/mtrl_alert_dialog_background_inset_start = 0x7f0600a5
expo.modules.devclient.test:id/snap = 0x7f080129
expo.modules.devclient.test:dimen/mtrl_btn_hovered_z = 0x7f0600ba
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f0f01c8
expo.modules.devclient.test:attr/textAppearanceOverline = 0x7f030271
expo.modules.devclient.test:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f0600a3
expo.modules.devclient.test:style/Base.Widget.AppCompat.Toolbar = 0x7f0f00d4
expo.modules.devclient.test:attr/listPreferredItemHeightLarge = 0x7f030198
expo.modules.devclient.test:dimen/material_emphasis_high_type = 0x7f06009f
expo.modules.devclient.test:string/mtrl_picker_out_of_range = 0x7f0e0070
expo.modules.devclient.test:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009b
expo.modules.devclient.test:dimen/hint_pressed_alpha_material_light = 0x7f06009a
expo.modules.devclient.test:drawable/abc_ratingbar_material = 0x7f070077
expo.modules.devclient.test:style/TextAppearance.AppCompat.Small = 0x7f0f014c
expo.modules.devclient.test:id/use_hardware_layer = 0x7f080168
expo.modules.devclient.test:dimen/highlight_alpha_material_light = 0x7f060096
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_settingsfilledinactiveicon = 0x7f070032
expo.modules.devclient.test:string/mtrl_badge_numberless_content_description = 0x7f0e005e
expo.modules.devclient.test:attr/buttonBarNeutralButtonStyle = 0x7f03006c
expo.modules.devclient.test:dimen/mtrl_card_spacing = 0x7f0600f8
expo.modules.devclient.test:dimen/highlight_alpha_material_dark = 0x7f060095
expo.modules.devclient.test:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070051
expo.modules.devclient.test:dimen/fastscroll_minimum_range = 0x7f060093
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f0f0282
expo.modules.devclient.test:dimen/disabled_alpha_material_dark = 0x7f06008f
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f0f01dd
expo.modules.devclient.test:drawable/notification_tile_bg = 0x7f0700cd
expo.modules.devclient.test:drawable/abc_scrubber_track_mtrl_alpha = 0x7f07007d
expo.modules.devclient.test:dimen/mtrl_alert_dialog_background_inset_end = 0x7f0600a4
expo.modules.devclient.test:layout/mtrl_layout_snackbar_include = 0x7f0b004a
expo.modules.devclient.test:dimen/design_textinput_caption_translate_y = 0x7f06008e
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f0f02a6
expo.modules.devclient.test:dimen/design_tab_text_size_2line = 0x7f06008d
expo.modules.devclient.test:dimen/design_snackbar_text_size = 0x7f060089
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f0f0067
expo.modules.devclient.test:string/icon_content_description = 0x7f0e0054
expo.modules.devclient.test:dimen/design_snackbar_padding_vertical_2lines = 0x7f060088
expo.modules.devclient.test:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f0f0014
expo.modules.devclient.test:dimen/mtrl_extended_fab_corner_radius = 0x7f060100
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Dark = 0x7f0f01fa
expo.modules.devclient.test:dimen/design_snackbar_min_width = 0x7f060085
expo.modules.devclient.test:attr/roundAsCircle = 0x7f0301f6
expo.modules.devclient.test:dimen/design_snackbar_max_width = 0x7f060084
expo.modules.devclient.test:dimen/design_snackbar_elevation = 0x7f060082
expo.modules.devclient.test:dimen/mtrl_btn_padding_right = 0x7f0600c1
expo.modules.devclient.test:dimen/design_snackbar_background_corner_radius = 0x7f060081
expo.modules.devclient.test:dimen/design_navigation_max_width = 0x7f06007c
expo.modules.devclient.test:dimen/design_fab_translation_z_pressed = 0x7f060076
expo.modules.devclient.test:string/state_mixed_description = 0x7f0e0090
expo.modules.devclient.test:dimen/design_fab_size_normal = 0x7f060074
expo.modules.devclient.test:attr/itemShapeInsetStart = 0x7f030170
expo.modules.devclient.test:attr/firstBaselineToTopHeight = 0x7f03012c
expo.modules.devclient.test:dimen/design_fab_size_mini = 0x7f060073
expo.modules.devclient.test:string/catalyst_settings = 0x7f0e003e
expo.modules.devclient.test:attr/backgroundStacked = 0x7f030047
expo.modules.devclient.test:dimen/design_fab_elevation = 0x7f060071
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f0038
expo.modules.devclient.test:dimen/design_fab_border_width = 0x7f060070
expo.modules.devclient.test:dimen/design_bottom_sheet_peek_height_min = 0x7f06006f
expo.modules.devclient.test:dimen/design_bottom_sheet_elevation = 0x7f06006d
expo.modules.devclient.test:dimen/mtrl_calendar_year_vertical_padding = 0x7f0600f1
expo.modules.devclient.test:id/rn_redbox_dismiss_button = 0x7f080101
expo.modules.devclient.test:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07007a
expo.modules.devclient.test:dimen/design_bottom_navigation_text_size = 0x7f06006c
expo.modules.devclient.test:dimen/design_bottom_navigation_item_min_width = 0x7f060069
expo.modules.devclient.test:dimen/design_bottom_navigation_height = 0x7f060066
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_buildingicon = 0x7f070017
expo.modules.devclient.test:dimen/design_bottom_navigation_active_item_max_width = 0x7f060062
expo.modules.devclient.test:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f0f0277
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_showmenuatlaunchicon = 0x7f070036
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f0f005c
expo.modules.devclient.test:dimen/default_dimension = 0x7f060060
expo.modules.devclient.test:integer/mtrl_badge_max_character_count = 0x7f090009
expo.modules.devclient.test:attr/itemStrokeWidth = 0x7f030174
expo.modules.devclient.test:dimen/compat_notification_large_icon_max_width = 0x7f06005f
expo.modules.devclient.test:dimen/hint_alpha_material_light = 0x7f060098
expo.modules.devclient.test:drawable/paused_in_debugger_dialog_background = 0x7f0700d0
expo.modules.devclient.test:attr/materialAlertDialogTitlePanelStyle = 0x7f0301a3
expo.modules.devclient.test:dimen/compat_button_padding_vertical_material = 0x7f06005c
expo.modules.devclient.test:dimen/browser_actions_context_menu_min_padding = 0x7f060055
expo.modules.devclient.test:dimen/abc_text_size_menu_header_material = 0x7f06004a
expo.modules.devclient.test:dimen/browser_actions_context_menu_max_width = 0x7f060054
expo.modules.devclient.test:dimen/design_navigation_icon_size = 0x7f060079
expo.modules.devclient.test:dimen/abc_text_size_title_material_toolbar = 0x7f060050
expo.modules.devclient.test:integer/mtrl_calendar_selection_text_lines = 0x7f09000d
expo.modules.devclient.test:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0600e1
expo.modules.devclient.test:dimen/abc_text_size_title_material = 0x7f06004f
expo.modules.devclient.test:dimen/abc_text_size_small_material = 0x7f06004c
expo.modules.devclient.test:drawable/$avd_show_password__0 = 0x7f070003
expo.modules.devclient.test:color/bright_foreground_material_dark = 0x7f050025
expo.modules.devclient.test:dimen/abc_text_size_medium_material = 0x7f060049
expo.modules.devclient.test:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f0f0276
expo.modules.devclient.test:dimen/notification_subtext_size = 0x7f060152
expo.modules.devclient.test:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f004a
expo.modules.devclient.test:dimen/abc_text_size_display_3_material = 0x7f060045
expo.modules.devclient.test:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f0187
expo.modules.devclient.test:dimen/abc_text_size_display_1_material = 0x7f060043
expo.modules.devclient.test:id/search_close_btn = 0x7f080117
expo.modules.devclient.test:dimen/abc_text_size_caption_material = 0x7f060042
expo.modules.devclient.test:dimen/abc_text_size_body_2_material = 0x7f060040
expo.modules.devclient.test:attr/textAppearanceSubtitle1 = 0x7f030276
expo.modules.devclient.test:dimen/abc_switch_padding = 0x7f06003e
expo.modules.devclient.test:dimen/mtrl_btn_elevation = 0x7f0600b8
expo.modules.devclient.test:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f0f0279
expo.modules.devclient.test:dimen/abc_star_big = 0x7f06003b
expo.modules.devclient.test:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f015a
expo.modules.devclient.test:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
expo.modules.devclient.test:dimen/abc_seekbar_track_background_height_material = 0x7f060038
expo.modules.devclient.test:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
expo.modules.devclient.test:dimen/abc_search_view_preferred_width = 0x7f060037
expo.modules.devclient.test:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
expo.modules.devclient.test:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f0f0278
expo.modules.devclient.test:id/tag_unhandled_key_listeners = 0x7f080141
expo.modules.devclient.test:dimen/abc_list_item_height_small_material = 0x7f060032
expo.modules.devclient.test:dimen/abc_dialog_min_width_minor = 0x7f060023
expo.modules.devclient.test:dimen/abc_list_item_height_material = 0x7f060031
expo.modules.devclient.test:id/confirm_button = 0x7f080072
expo.modules.devclient.test:attr/colorPrimaryVariant = 0x7f0300bb
expo.modules.devclient.test:dimen/abc_list_item_height_large_material = 0x7f060030
expo.modules.devclient.test:drawable/material_ic_calendar_black_24dp = 0x7f0700b1
expo.modules.devclient.test:attr/roundBottomStart = 0x7f0301fa
expo.modules.devclient.test:dimen/abc_floating_window_z = 0x7f06002f
expo.modules.devclient.test:style/TextAppearance.Compat.Notification.Title = 0x7f0f016a
expo.modules.devclient.test:color/mtrl_scrim_color = 0x7f0500b1
expo.modules.devclient.test:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
expo.modules.devclient.test:dimen/abc_disabled_alpha_material_light = 0x7f060028
expo.modules.devclient.test:dimen/mtrl_slider_track_top = 0x7f06012f
expo.modules.devclient.test:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
expo.modules.devclient.test:dimen/abc_disabled_alpha_material_dark = 0x7f060027
expo.modules.devclient.test:dimen/tooltip_precise_anchor_threshold = 0x7f06015a
expo.modules.devclient.test:dimen/abc_dialog_title_divider_material = 0x7f060026
expo.modules.devclient.test:styleable/DrawerArrowToggle = 0x7f100024
expo.modules.devclient.test:id/accessibility_custom_action_19 = 0x7f080019
expo.modules.devclient.test:dimen/abc_text_size_button_material = 0x7f060041
expo.modules.devclient.test:dimen/abc_dialog_padding_top_material = 0x7f060025
expo.modules.devclient.test:attr/snackbarTextViewStyle = 0x7f030220
expo.modules.devclient.test:dimen/abc_dialog_padding_material = 0x7f060024
expo.modules.devclient.test:attr/singleLine = 0x7f03021b
expo.modules.devclient.test:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
expo.modules.devclient.test:attr/extendMotionSpec = 0x7f03011b
expo.modules.devclient.test:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
expo.modules.devclient.test:anim/mtrl_bottom_sheet_slide_out = 0x7f010024
expo.modules.devclient.test:id/accessibility_custom_action_30 = 0x7f080026
expo.modules.devclient.test:dimen/abc_dialog_fixed_width_major = 0x7f06001e
expo.modules.devclient.test:id/tag_accessibility_heading = 0x7f080138
expo.modules.devclient.test:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f060138
expo.modules.devclient.test:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f0f00db
expo.modules.devclient.test:dimen/abc_control_padding_material = 0x7f06001a
expo.modules.devclient.test:dimen/abc_control_inset_material = 0x7f060019
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f0037
expo.modules.devclient.test:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
expo.modules.devclient.test:string/material_slider_range_start = 0x7f0e005a
expo.modules.devclient.test:dimen/abc_button_padding_vertical_material = 0x7f060015
expo.modules.devclient.test:string/mtrl_picker_toggle_to_year_selection = 0x7f0e0080
expo.modules.devclient.test:id/text = 0x7f080147
expo.modules.devclient.test:attr/menu = 0x7f0301ba
expo.modules.devclient.test:dimen/abc_button_padding_horizontal_material = 0x7f060014
expo.modules.devclient.test:dimen/abc_button_inset_vertical_material = 0x7f060013
expo.modules.devclient.test:dimen/mtrl_shape_corner_size_large_component = 0x7f060124
expo.modules.devclient.test:dimen/abc_button_inset_horizontal_material = 0x7f060012
expo.modules.devclient.test:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f0195
expo.modules.devclient.test:dimen/mtrl_extended_fab_disabled_elevation = 0x7f060101
expo.modules.devclient.test:dimen/design_snackbar_padding_horizontal = 0x7f060086
expo.modules.devclient.test:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
expo.modules.devclient.test:dimen/abc_progress_bar_height_material = 0x7f060035
expo.modules.devclient.test:attr/nestedScrollViewStyle = 0x7f0301c1
expo.modules.devclient.test:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
expo.modules.devclient.test:style/Base.Widget.AppCompat.Button = 0x7f0f00a7
expo.modules.devclient.test:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
expo.modules.devclient.test:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
expo.modules.devclient.test:attr/fadeDuration = 0x7f030124
expo.modules.devclient.test:dimen/abc_action_bar_content_inset_material = 0x7f060000
expo.modules.devclient.test:drawable/notification_bg_low = 0x7f0700c4
expo.modules.devclient.test:dimen/mtrl_high_ripple_pressed_alpha = 0x7f060117
expo.modules.devclient.test:color/test_mtrl_calendar_day_selected = 0x7f0500d5
expo.modules.devclient.test:drawable/mtrl_popupmenu_background = 0x7f0700be
expo.modules.devclient.test:attr/drawableEndCompat = 0x7f0300f0
expo.modules.devclient.test:color/switch_thumb_normal_material_light = 0x7f0500d3
expo.modules.devclient.test:attr/panelMenuListWidth = 0x7f0301cf
expo.modules.devclient.test:color/switch_thumb_material_dark = 0x7f0500d0
expo.modules.devclient.test:style/Theme.MaterialComponents.Bridge = 0x7f0f01b2
expo.modules.devclient.test:dimen/mtrl_shape_corner_size_medium_component = 0x7f060125
expo.modules.devclient.test:color/switch_thumb_disabled_material_light = 0x7f0500cf
expo.modules.devclient.test:color/dev_launcher_white = 0x7f050063
expo.modules.devclient.test:color/secondary_text_disabled_material_light = 0x7f0500cd
expo.modules.devclient.test:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070052
expo.modules.devclient.test:id/search_go_btn = 0x7f080119
expo.modules.devclient.test:color/secondary_text_default_material_light = 0x7f0500cb
expo.modules.devclient.test:attr/passwordToggleTint = 0x7f0301d3
expo.modules.devclient.test:dimen/fastscroll_margin = 0x7f060092
expo.modules.devclient.test:attr/colorSecondaryVariant = 0x7f0300bd
expo.modules.devclient.test:attr/dayInvalidStyle = 0x7f0300e2
expo.modules.devclient.test:color/secondary_text_default_material_dark = 0x7f0500ca
expo.modules.devclient.test:attr/listPreferredItemHeight = 0x7f030197
expo.modules.devclient.test:color/ripple_material_light = 0x7f0500c9
expo.modules.devclient.test:color/ripple_material_dark = 0x7f0500c8
expo.modules.devclient.test:styleable/Capability = 0x7f100019
expo.modules.devclient.test:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070088
expo.modules.devclient.test:color/primary_text_disabled_material_dark = 0x7f0500c5
expo.modules.devclient.test:color/primary_text_default_material_light = 0x7f0500c4
expo.modules.devclient.test:layout/notification_template_custom_big = 0x7f0b0057
expo.modules.devclient.test:attr/lStar = 0x7f03017a
expo.modules.devclient.test:color/primary_material_light = 0x7f0500c2
expo.modules.devclient.test:style/Widget.AppCompat.Button.Colored = 0x7f0f0225
expo.modules.devclient.test:string/mtrl_picker_text_input_date_range_start_hint = 0x7f0e0079
expo.modules.devclient.test:color/switch_thumb_material_light = 0x7f0500d1
expo.modules.devclient.test:color/primary_material_dark = 0x7f0500c1
expo.modules.devclient.test:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f0f0117
expo.modules.devclient.test:string/toolbar_description = 0x7f0e0098
expo.modules.devclient.test:color/primary_dark_material_light = 0x7f0500c0
expo.modules.devclient.test:color/primary_dark_material_dark = 0x7f0500bf
expo.modules.devclient.test:style/Widget.MaterialComponents.ChipGroup = 0x7f0f0298
expo.modules.devclient.test:dimen/design_navigation_icon_padding = 0x7f060078
expo.modules.devclient.test:color/notification_icon_bg_color = 0x7f0500be
expo.modules.devclient.test:id/icon_group = 0x7f0800b2
expo.modules.devclient.test:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0500bc
expo.modules.devclient.test:drawable/ripple_effect = 0x7f0700d2
expo.modules.devclient.test:styleable/TabLayout = 0x7f10005c
expo.modules.devclient.test:dimen/abc_text_size_large_material = 0x7f060048
expo.modules.devclient.test:color/notification_action_color_filter = 0x7f0500bd
expo.modules.devclient.test:color/mtrl_textinput_disabled_color = 0x7f0500b9
expo.modules.devclient.test:color/mtrl_textinput_default_box_stroke_color = 0x7f0500b8
expo.modules.devclient.test:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f0f0080
expo.modules.devclient.test:dimen/notification_main_column_padding_top = 0x7f06014c
expo.modules.devclient.test:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0600e7
expo.modules.devclient.test:attr/contentInsetLeft = 0x7f0300c4
expo.modules.devclient.test:color/mtrl_text_btn_text_color_selector = 0x7f0500b7
expo.modules.devclient.test:dimen/abc_alert_dialog_button_dimen = 0x7f060011
expo.modules.devclient.test:style/CalendarDatePickerDialog = 0x7f0f00e2
expo.modules.devclient.test:color/mtrl_tabs_icon_color_selector_colored = 0x7f0500b4
expo.modules.devclient.test:attr/autoSizePresetSizes = 0x7f030036
expo.modules.devclient.test:color/mtrl_tabs_icon_color_selector = 0x7f0500b3
expo.modules.devclient.test:color/mtrl_tabs_colored_ripple_color = 0x7f0500b2
expo.modules.devclient.test:attr/navigationMode = 0x7f0301bf
expo.modules.devclient.test:color/mtrl_popupmenu_overlay_color = 0x7f0500b0
expo.modules.devclient.test:color/mtrl_outlined_icon_tint = 0x7f0500ae
expo.modules.devclient.test:attr/scrimVisibleHeightTrigger = 0x7f030206
expo.modules.devclient.test:drawable/design_fab_background = 0x7f07009f
expo.modules.devclient.test:styleable/ForegroundLinearLayout = 0x7f10002c
expo.modules.devclient.test:dimen/compat_button_inset_horizontal_material = 0x7f060059
expo.modules.devclient.test:color/mtrl_navigation_item_icon_tint = 0x7f0500ab
expo.modules.devclient.test:color/material_blue_grey_800 = 0x7f05006e
expo.modules.devclient.test:color/mtrl_navigation_item_background_color = 0x7f0500aa
expo.modules.devclient.test:color/mtrl_filled_stroke_color = 0x7f0500a8
expo.modules.devclient.test:id/tag_on_receive_content_mime_types = 0x7f08013c
expo.modules.devclient.test:attr/lineHeight = 0x7f03018d
expo.modules.devclient.test:color/mtrl_filled_icon_tint = 0x7f0500a7
expo.modules.devclient.test:id/design_menu_item_action_area_stub = 0x7f080081
expo.modules.devclient.test:color/mtrl_fab_ripple_color = 0x7f0500a5
expo.modules.devclient.test:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07004a
expo.modules.devclient.test:styleable/ActionMode = 0x7f100004
expo.modules.devclient.test:style/TextAppearance.AppCompat.Headline = 0x7f0f013f
expo.modules.devclient.test:color/mtrl_fab_icon_text_color_selector = 0x7f0500a4
expo.modules.devclient.test:styleable/ActionMenuView = 0x7f100003
expo.modules.devclient.test:dimen/mtrl_fab_translation_z_pressed = 0x7f060113
expo.modules.devclient.test:color/primary_text_disabled_material_light = 0x7f0500c6
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f0024
expo.modules.devclient.test:id/customPanel = 0x7f08007a
expo.modules.devclient.test:attr/buttonStyleSmall = 0x7f030074
expo.modules.devclient.test:color/mtrl_fab_bg_color_selector = 0x7f0500a3
expo.modules.devclient.test:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f08016e
expo.modules.devclient.test:color/mtrl_choice_chip_ripple_color = 0x7f0500a0
expo.modules.devclient.test:id/clip_vertical = 0x7f080070
expo.modules.devclient.test:attr/helperTextTextAppearance = 0x7f030144
expo.modules.devclient.test:color/mtrl_chip_surface_color = 0x7f05009d
expo.modules.devclient.test:color/mtrl_chip_close_icon_tint = 0x7f05009b
expo.modules.devclient.test:color/mtrl_chip_background_color = 0x7f05009a
expo.modules.devclient.test:interpolator/fast_out_slow_in = 0x7f0a0006
expo.modules.devclient.test:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070049
expo.modules.devclient.test:attr/maxButtonHeight = 0x7f0301b5
expo.modules.devclient.test:color/design_dark_default_color_on_background = 0x7f05003a
expo.modules.devclient.test:color/mtrl_calendar_selected_range = 0x7f050097
expo.modules.devclient.test:style/ShapeAppearance.MaterialComponents.Test = 0x7f0f0118
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f0f0068
expo.modules.devclient.test:string/mtrl_picker_invalid_format_use = 0x7f0e006d
expo.modules.devclient.test:color/mtrl_calendar_item_stroke_color = 0x7f050096
expo.modules.devclient.test:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f0f02cf
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0234
expo.modules.devclient.test:string/call_notification_incoming_text = 0x7f0e0023
expo.modules.devclient.test:color/mtrl_btn_transparent_bg_color = 0x7f050095
expo.modules.devclient.test:style/Base.V26.Theme.AppCompat.Light = 0x7f0f0090
expo.modules.devclient.test:color/mtrl_btn_text_color_selector = 0x7f050094
expo.modules.devclient.test:dimen/abc_text_size_display_2_material = 0x7f060044
expo.modules.devclient.test:color/mtrl_btn_text_color_disabled = 0x7f050093
expo.modules.devclient.test:dimen/disabled_alpha_material_light = 0x7f060090
expo.modules.devclient.test:color/mtrl_btn_text_btn_ripple_color = 0x7f050092
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f0f0288
expo.modules.devclient.test:attr/fastScrollHorizontalThumbDrawable = 0x7f030128
expo.modules.devclient.test:color/mtrl_btn_text_btn_bg_color_selector = 0x7f050091
expo.modules.devclient.test:color/mtrl_btn_stroke_color_selector = 0x7f050090
expo.modules.devclient.test:styleable/CollapsingToolbarLayout = 0x7f10001e
expo.modules.devclient.test:attr/spinnerDropDownItemStyle = 0x7f030223
expo.modules.devclient.test:color/mtrl_btn_bg_color_selector = 0x7f05008e
expo.modules.devclient.test:drawable/abc_list_selector_holo_light = 0x7f070073
expo.modules.devclient.test:color/mtrl_bottom_nav_ripple_color = 0x7f05008d
expo.modules.devclient.test:color/mtrl_bottom_nav_item_tint = 0x7f05008c
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light = 0x7f0f0060
expo.modules.devclient.test:anim/abc_tooltip_exit = 0x7f01000b
expo.modules.devclient.test:color/dim_foreground_material_dark = 0x7f050066
expo.modules.devclient.test:color/mtrl_bottom_nav_colored_ripple_color = 0x7f05008b
expo.modules.devclient.test:attr/barLength = 0x7f03004d
expo.modules.devclient.test:color/material_slider_thumb_color = 0x7f050089
expo.modules.devclient.test:layout/abc_screen_toolbar = 0x7f0b0017
expo.modules.devclient.test:color/material_slider_inactive_track_color = 0x7f050088
expo.modules.devclient.test:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f00af
expo.modules.devclient.test:layout/abc_popup_menu_item_layout = 0x7f0b0013
expo.modules.devclient.test:attr/actionProviderClass = 0x7f030021
expo.modules.devclient.test:color/material_slider_halo_color = 0x7f050086
expo.modules.devclient.test:layout/design_text_input_end_icon = 0x7f0b002f
expo.modules.devclient.test:color/material_on_surface_stroke = 0x7f050083
expo.modules.devclient.test:color/material_on_surface_disabled = 0x7f050080
expo.modules.devclient.test:animator/fragment_close_exit = 0x7f020004
expo.modules.devclient.test:color/material_on_background_emphasis_medium = 0x7f05007c
expo.modules.devclient.test:color/secondary_text_disabled_material_dark = 0x7f0500cc
expo.modules.devclient.test:color/material_on_background_emphasis_high_type = 0x7f05007b
expo.modules.devclient.test:color/material_grey_800 = 0x7f050077
expo.modules.devclient.test:color/material_grey_50 = 0x7f050075
expo.modules.devclient.test:color/material_blue_grey_950 = 0x7f050070
expo.modules.devclient.test:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f0151
expo.modules.devclient.test:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f00cd
expo.modules.devclient.test:id/titleDividerNoCustom = 0x7f080157
expo.modules.devclient.test:attr/windowFixedWidthMinor = 0x7f0302b8
expo.modules.devclient.test:color/highlighted_text_material_light = 0x7f05006d
expo.modules.devclient.test:color/foreground_material_light = 0x7f05006b
expo.modules.devclient.test:style/Widget.AppCompat.Button.Borderless = 0x7f0f0222
expo.modules.devclient.test:attr/commitIcon = 0x7f0300c0
expo.modules.devclient.test:color/foreground_material_dark = 0x7f05006a
expo.modules.devclient.test:drawable/abc_list_longpressed_holo = 0x7f07006b
expo.modules.devclient.test:styleable/FragmentContainerView = 0x7f10002e
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0160
expo.modules.devclient.test:color/dim_foreground_material_light = 0x7f050067
expo.modules.devclient.test:color/mtrl_tabs_ripple_color = 0x7f0500b6
expo.modules.devclient.test:string/timer_description = 0x7f0e0097
expo.modules.devclient.test:attr/thumbColor = 0x7f030282
expo.modules.devclient.test:color/highlighted_text_material_dark = 0x7f05006c
expo.modules.devclient.test:style/WhiteBackgroundTheme = 0x7f0f0215
expo.modules.devclient.test:color/dim_foreground_disabled_material_light = 0x7f050065
expo.modules.devclient.test:styleable/SnackbarLayout = 0x7f100054
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_homefilledactiveicon = 0x7f070023
expo.modules.devclient.test:id/mtrl_picker_header_toggle = 0x7f0800de
expo.modules.devclient.test:color/dev_launcher_secondaryBackgroundColor = 0x7f050062
expo.modules.devclient.test:drawable/navigation_empty_icon = 0x7f0700c1
expo.modules.devclient.test:dimen/abc_action_bar_stacked_max_height = 0x7f060009
expo.modules.devclient.test:color/dev_launcher_errorLogButton = 0x7f05005f
expo.modules.devclient.test:id/chronometer = 0x7f08006d
expo.modules.devclient.test:attr/font = 0x7f03012e
expo.modules.devclient.test:dimen/material_text_view_test_line_height_override = 0x7f0600a2
expo.modules.devclient.test:color/dev_launcher_colorPrimaryDark = 0x7f05005e
expo.modules.devclient.test:style/Widget.AppCompat.Toolbar = 0x7f0f025d
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f0f00f3
expo.modules.devclient.test:attr/tabPaddingBottom = 0x7f030257
expo.modules.devclient.test:color/design_icon_tint = 0x7f05005a
expo.modules.devclient.test:attr/paddingBottomSystemWindowInsets = 0x7f0301c7
expo.modules.devclient.test:dimen/mtrl_calendar_bottom_padding = 0x7f0600cc
expo.modules.devclient.test:id/italic = 0x7f0800b7
expo.modules.devclient.test:attr/contentScrim = 0x7f0300cd
expo.modules.devclient.test:color/design_fab_stroke_top_outer_color = 0x7f050059
expo.modules.devclient.test:dimen/mtrl_calendar_header_height_fullscreen = 0x7f0600da
expo.modules.devclient.test:id/tag_on_apply_window_listener = 0x7f08013a
expo.modules.devclient.test:color/design_fab_stroke_top_inner_color = 0x7f050058
expo.modules.devclient.test:layout/error_console_fragment = 0x7f0b0033
expo.modules.devclient.test:id/design_menu_item_text = 0x7f080082
expo.modules.devclient.test:color/design_fab_stroke_end_inner_color = 0x7f050056
expo.modules.devclient.test:string/combobox_description = 0x7f0e0045
expo.modules.devclient.test:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
expo.modules.devclient.test:color/design_fab_shadow_mid_color = 0x7f050054
expo.modules.devclient.test:color/design_fab_shadow_end_color = 0x7f050053
expo.modules.devclient.test:string/abc_menu_sym_shortcut_label = 0x7f0e0010
expo.modules.devclient.test:dimen/mtrl_extended_fab_translation_z_base = 0x7f06010d
expo.modules.devclient.test:color/design_error = 0x7f050052
expo.modules.devclient.test:color/design_default_color_surface = 0x7f050051
expo.modules.devclient.test:color/design_default_color_secondary = 0x7f05004f
expo.modules.devclient.test:drawable/abc_ic_voice_search_api_material = 0x7f070065
expo.modules.devclient.test:string/radiogroup_description = 0x7f0e0087
expo.modules.devclient.test:color/mtrl_btn_ripple_color = 0x7f05008f
expo.modules.devclient.test:color/design_default_color_primary_variant = 0x7f05004e
expo.modules.devclient.test:attr/colorSurface = 0x7f0300be
expo.modules.devclient.test:color/design_default_color_primary_dark = 0x7f05004d
expo.modules.devclient.test:color/design_default_color_on_surface = 0x7f05004b
expo.modules.devclient.test:drawable/btn_radio_on_mtrl = 0x7f07009c
expo.modules.devclient.test:color/design_default_color_on_secondary = 0x7f05004a
expo.modules.devclient.test:attr/counterOverflowTextColor = 0x7f0300de
expo.modules.devclient.test:color/design_default_color_on_background = 0x7f050047
expo.modules.devclient.test:color/design_dark_default_color_surface = 0x7f050044
expo.modules.devclient.test:color/design_dark_default_color_secondary = 0x7f050042
expo.modules.devclient.test:color/button_material_dark = 0x7f05002b
expo.modules.devclient.test:color/design_dark_default_color_primary = 0x7f05003f
expo.modules.devclient.test:dimen/cardview_compat_inset_shadow = 0x7f060056
expo.modules.devclient.test:color/design_dark_default_color_on_surface = 0x7f05003e
expo.modules.devclient.test:color/switch_thumb_disabled_material_dark = 0x7f0500ce
expo.modules.devclient.test:color/design_dark_default_color_on_secondary = 0x7f05003d
expo.modules.devclient.test:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009c
expo.modules.devclient.test:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060083
expo.modules.devclient.test:id/snapMargins = 0x7f08012a
expo.modules.devclient.test:anim/design_bottom_sheet_slide_out = 0x7f01001f
expo.modules.devclient.test:color/design_dark_default_color_on_primary = 0x7f05003c
expo.modules.devclient.test:color/design_dark_default_color_on_error = 0x7f05003b
expo.modules.devclient.test:color/design_dark_default_color_background = 0x7f050038
expo.modules.devclient.test:attr/thumbElevation = 0x7f030283
expo.modules.devclient.test:color/design_box_stroke_color = 0x7f050037
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f0f02ad
expo.modules.devclient.test:id/never = 0x7f0800e5
expo.modules.devclient.test:dimen/abc_action_bar_elevation_material = 0x7f060005
expo.modules.devclient.test:style/TestStyleWithLineHeightAppearance = 0x7f0f0131
expo.modules.devclient.test:color/checkbox_themeable_attribute_color = 0x7f050035
expo.modules.devclient.test:color/catalyst_logbox_background = 0x7f050033
expo.modules.devclient.test:string/call_notification_answer_action = 0x7f0e001f
expo.modules.devclient.test:id/transform_origin = 0x7f08015d
expo.modules.devclient.test:color/cardview_shadow_end_color = 0x7f050031
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f006d
expo.modules.devclient.test:color/cardview_light_background = 0x7f050030
expo.modules.devclient.test:color/dim_foreground_disabled_material_dark = 0x7f050064
expo.modules.devclient.test:id/labelled_by = 0x7f0800ba
expo.modules.devclient.test:attr/selectionRequired = 0x7f03020d
expo.modules.devclient.test:color/cardview_dark_background = 0x7f05002f
expo.modules.devclient.test:dimen/tooltip_corner_radius = 0x7f060156
expo.modules.devclient.test:attr/materialCalendarDay = 0x7f0301a8
expo.modules.devclient.test:color/browser_actions_text_color = 0x7f050029
expo.modules.devclient.test:color/browser_actions_bg_grey = 0x7f050027
expo.modules.devclient.test:styleable/MaterialCardView = 0x7f10003d
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f0f02a7
expo.modules.devclient.test:color/bright_foreground_material_light = 0x7f050026
expo.modules.devclient.test:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020013
expo.modules.devclient.test:attr/tabIndicatorColor = 0x7f03024e
expo.modules.devclient.test:color/accent_material_dark = 0x7f050019
expo.modules.devclient.test:styleable/ShapeableImageView = 0x7f100050
expo.modules.devclient.test:style/ThemeOverlay.AppCompat.Light = 0x7f0f01ec
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0056
expo.modules.devclient.test:attr/editTextColor = 0x7f0300fc
expo.modules.devclient.test:color/abc_tint_switch_track = 0x7f050018
expo.modules.devclient.test:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07004f
expo.modules.devclient.test:id/mix_blend_mode = 0x7f0800c7
expo.modules.devclient.test:drawable/mtrl_popupmenu_background_dark = 0x7f0700bf
expo.modules.devclient.test:color/abc_tint_edittext = 0x7f050015
expo.modules.devclient.test:color/abc_search_url_text_pressed = 0x7f05000f
expo.modules.devclient.test:color/mtrl_card_view_foreground = 0x7f050098
expo.modules.devclient.test:style/Widget.MaterialComponents.Chip.Choice = 0x7f0f0295
expo.modules.devclient.test:attr/itemShapeAppearance = 0x7f03016b
expo.modules.devclient.test:color/abc_search_url_text_normal = 0x7f05000e
expo.modules.devclient.test:string/catalyst_perf_monitor = 0x7f0e0037
expo.modules.devclient.test:color/abc_search_url_text = 0x7f05000d
expo.modules.devclient.test:id/normal = 0x7f0800e8
expo.modules.devclient.test:color/material_slider_active_track_color = 0x7f050085
expo.modules.devclient.test:attr/actionModeCloseContentDescription = 0x7f030012
expo.modules.devclient.test:style/Widget.Design.BottomNavigationView = 0x7f0f0268
expo.modules.devclient.test:color/abc_hint_foreground_material_light = 0x7f050008
expo.modules.devclient.test:anim/mtrl_bottom_sheet_slide_in = 0x7f010023
expo.modules.devclient.test:attr/windowFixedHeightMajor = 0x7f0302b5
expo.modules.devclient.test:color/abc_hint_foreground_material_dark = 0x7f050007
expo.modules.devclient.test:color/abc_decor_view_status_guard = 0x7f050005
expo.modules.devclient.test:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
expo.modules.devclient.test:color/dev_launcher_errorMessage = 0x7f050060
expo.modules.devclient.test:color/abc_btn_colored_text_material = 0x7f050003
expo.modules.devclient.test:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f0f02be
expo.modules.devclient.test:color/material_on_background_disabled = 0x7f05007a
expo.modules.devclient.test:anim/abc_grow_fade_in_from_bottom = 0x7f010002
expo.modules.devclient.test:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
expo.modules.devclient.test:color/abc_btn_colored_borderless_text_material = 0x7f050002
expo.modules.devclient.test:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
expo.modules.devclient.test:attr/textLocale = 0x7f03027d
expo.modules.devclient.test:color/abc_primary_text_disable_only_material_dark = 0x7f050009
expo.modules.devclient.test:styleable/ChipGroup = 0x7f10001d
expo.modules.devclient.test:attr/windowNoTitle = 0x7f0302bb
expo.modules.devclient.test:drawable/mtrl_tabs_default_indicator = 0x7f0700c0
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f0157
expo.modules.devclient.test:attr/itemIconSize = 0x7f030166
expo.modules.devclient.test:color/design_fab_stroke_end_outer_color = 0x7f050057
expo.modules.devclient.test:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
expo.modules.devclient.test:string/catalyst_change_bundle_location = 0x7f0e0026
expo.modules.devclient.test:attr/windowActionBarOverlay = 0x7f0302b3
expo.modules.devclient.test:attr/windowActionBar = 0x7f0302b2
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f00a5
expo.modules.devclient.test:attr/voiceIcon = 0x7f0302b1
expo.modules.devclient.test:dimen/mtrl_tooltip_minHeight = 0x7f060142
expo.modules.devclient.test:attr/trackTintMode = 0x7f0302a8
expo.modules.devclient.test:attr/trackHeight = 0x7f0302a6
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0236
expo.modules.devclient.test:dimen/compat_notification_large_icon_max_height = 0x7f06005e
expo.modules.devclient.test:layout/design_layout_tab_icon = 0x7f0b0026
expo.modules.devclient.test:attr/counterOverflowTextAppearance = 0x7f0300dd
expo.modules.devclient.test:attr/trackColorInactive = 0x7f0302a5
expo.modules.devclient.test:dimen/tooltip_margin = 0x7f060158
expo.modules.devclient.test:style/TestStyleWithLineHeight = 0x7f0f0130
expo.modules.devclient.test:layout/abc_cascading_menu_item_layout = 0x7f0b000b
expo.modules.devclient.test:attr/theme = 0x7f03027f
expo.modules.devclient.test:attr/tooltipStyle = 0x7f0302a0
expo.modules.devclient.test:color/design_dark_default_color_primary_variant = 0x7f050041
expo.modules.devclient.test:attr/tooltipForegroundColor = 0x7f03029e
expo.modules.devclient.test:drawable/$avd_hide_password__1 = 0x7f070001
expo.modules.devclient.test:color/tooltip_background_light = 0x7f0500d7
expo.modules.devclient.test:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
expo.modules.devclient.test:attr/toolbarStyle = 0x7f03029d
expo.modules.devclient.test:attr/actionBarSplitStyle = 0x7f030004
expo.modules.devclient.test:anim/abc_popup_exit = 0x7f010004
expo.modules.devclient.test:attr/lineSpacing = 0x7f03018e
expo.modules.devclient.test:attr/titleMargins = 0x7f030297
expo.modules.devclient.test:dimen/design_navigation_separator_vertical_padding = 0x7f06007e
expo.modules.devclient.test:attr/titleMarginTop = 0x7f030296
expo.modules.devclient.test:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0228
expo.modules.devclient.test:attr/appBarLayoutStyle = 0x7f030030
expo.modules.devclient.test:attr/titleMarginStart = 0x7f030295
expo.modules.devclient.test:attr/titleMarginEnd = 0x7f030294
expo.modules.devclient.test:attr/backgroundImage = 0x7f030040
expo.modules.devclient.test:anim/abc_slide_out_top = 0x7f010009
expo.modules.devclient.test:attr/track = 0x7f0302a2
expo.modules.devclient.test:attr/closeIcon = 0x7f03009e
expo.modules.devclient.test:attr/titleMargin = 0x7f030292
expo.modules.devclient.test:layout/design_navigation_item_subheader = 0x7f0b002c
expo.modules.devclient.test:attr/colorPrimarySurface = 0x7f0300ba
expo.modules.devclient.test:color/design_bottom_navigation_shadow_color = 0x7f050036
expo.modules.devclient.test:attr/title = 0x7f030290
expo.modules.devclient.test:attr/tickMarkTintMode = 0x7f03028d
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f0f007d
expo.modules.devclient.test:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f0f02a9
expo.modules.devclient.test:attr/useMaterialThemeColors = 0x7f0302ac
expo.modules.devclient.test:attr/tickMarkTint = 0x7f03028c
expo.modules.devclient.test:anim/abc_tooltip_enter = 0x7f01000a
expo.modules.devclient.test:style/Base.Widget.AppCompat.ImageButton = 0x7f0f00b6
expo.modules.devclient.test:attr/viewAspectRatio = 0x7f0302af
expo.modules.devclient.test:attr/tabMaxWidth = 0x7f030253
expo.modules.devclient.test:attr/tickColor = 0x7f030288
expo.modules.devclient.test:dimen/appcompat_dialog_background_inset = 0x7f060052
expo.modules.devclient.test:attr/thumbTextPadding = 0x7f030285
expo.modules.devclient.test:id/action_mode_close_button = 0x7f080044
expo.modules.devclient.test:dimen/notification_right_icon_size = 0x7f06014e
expo.modules.devclient.test:dimen/mtrl_slider_halo_radius = 0x7f060127
expo.modules.devclient.test:attr/fontProviderFetchStrategy = 0x7f030132
expo.modules.devclient.test:dimen/compat_button_inset_vertical_material = 0x7f06005a
expo.modules.devclient.test:attr/thickness = 0x7f030281
expo.modules.devclient.test:drawable/abc_list_selector_holo_dark = 0x7f070072
expo.modules.devclient.test:attr/textInputStyle = 0x7f03027c
expo.modules.devclient.test:attr/customNavigationLayout = 0x7f0300e1
expo.modules.devclient.test:drawable/ic_mtrl_chip_checked_circle = 0x7f0700ae
expo.modules.devclient.test:attr/textEndPadding = 0x7f03027a
expo.modules.devclient.test:id/accessibility_custom_action_26 = 0x7f080021
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_showmenuatlaunchiconlight = 0x7f070037
expo.modules.devclient.test:attr/actionModeSelectAllDrawable = 0x7f030019
expo.modules.devclient.test:color/error_color_material_light = 0x7f050069
expo.modules.devclient.test:attr/textColorSearchUrl = 0x7f030279
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_shakedeviceiconlight = 0x7f070035
expo.modules.devclient.test:drawable/abc_seekbar_thumb_material = 0x7f07007e
expo.modules.devclient.test:attr/contentInsetStartWithNavigation = 0x7f0300c7
expo.modules.devclient.test:color/abc_color_highlight_material = 0x7f050004
expo.modules.devclient.test:styleable/RecyclerView = 0x7f10004b
expo.modules.devclient.test:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0600f0
expo.modules.devclient.test:attr/textColorAlertDialogListItem = 0x7f030278
expo.modules.devclient.test:style/Widget.MaterialComponents.TabLayout = 0x7f0f02bd
expo.modules.devclient.test:integer/mtrl_card_anim_duration_ms = 0x7f090010
expo.modules.devclient.test:attr/actionModeCutDrawable = 0x7f030015
expo.modules.devclient.test:color/material_blue_grey_900 = 0x7f05006f
expo.modules.devclient.test:attr/textAppearanceSmallPopupMenu = 0x7f030275
expo.modules.devclient.test:color/material_grey_850 = 0x7f050078
expo.modules.devclient.test:dimen/mtrl_extended_fab_start_padding_icon = 0x7f06010b
expo.modules.devclient.test:styleable/AlertDialog = 0x7f100006
expo.modules.devclient.test:attr/textAppearanceSearchResultTitle = 0x7f030274
expo.modules.devclient.test:id/row_index_key = 0x7f08010a
expo.modules.devclient.test:attr/textAppearancePopupMenuHeader = 0x7f030272
expo.modules.devclient.test:attr/dropdownListPreferredItemHeight = 0x7f0300fa
expo.modules.devclient.test:dimen/mtrl_calendar_year_width = 0x7f0600f2
expo.modules.devclient.test:attr/textAppearanceLargePopupMenu = 0x7f03026c
expo.modules.devclient.test:attr/spinnerStyle = 0x7f030224
expo.modules.devclient.test:attr/behavior_fitToContents = 0x7f030052
expo.modules.devclient.test:styleable/AppBarLayout_Layout = 0x7f10000c
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f009f
expo.modules.devclient.test:attr/textAppearanceHeadline2 = 0x7f030267
expo.modules.devclient.test:attr/buttonTint = 0x7f030075
expo.modules.devclient.test:attr/splitTrack = 0x7f030225
expo.modules.devclient.test:dimen/design_bottom_navigation_item_max_width = 0x7f060068
expo.modules.devclient.test:attr/textAppearanceHeadline1 = 0x7f030266
expo.modules.devclient.test:attr/fontProviderCerts = 0x7f030131
expo.modules.devclient.test:attr/textAppearanceButton = 0x7f030264
expo.modules.devclient.test:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0254
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f0f0120
expo.modules.devclient.test:color/design_default_color_primary = 0x7f05004c
expo.modules.devclient.test:attr/textAppearanceHeadline4 = 0x7f030269
expo.modules.devclient.test:attr/textAppearanceBody2 = 0x7f030263
expo.modules.devclient.test:dimen/mtrl_tooltip_cornerSize = 0x7f060141
expo.modules.devclient.test:attr/stackFromEnd = 0x7f030227
expo.modules.devclient.test:dimen/cardview_default_elevation = 0x7f060057
expo.modules.devclient.test:attr/cardMaxElevation = 0x7f03007b
expo.modules.devclient.test:attr/textAppearanceBody1 = 0x7f030262
expo.modules.devclient.test:attr/buttonBarButtonStyle = 0x7f03006a
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_warningtriangleiconlight = 0x7f070041
expo.modules.devclient.test:attr/tabTextColor = 0x7f03025f
expo.modules.devclient.test:attr/boxCornerRadiusTopStart = 0x7f030065
expo.modules.devclient.test:attr/tabStyle = 0x7f03025d
expo.modules.devclient.test:attr/tabSelectedTextColor = 0x7f03025c
expo.modules.devclient.test:id/textinput_prefix_text = 0x7f080153
expo.modules.devclient.test:attr/layout = 0x7f03017f
expo.modules.devclient.test:drawable/abc_list_selector_disabled_holo_light = 0x7f070071
expo.modules.devclient.test:attr/thumbTint = 0x7f030286
expo.modules.devclient.test:attr/tabPaddingEnd = 0x7f030258
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f0f027f
expo.modules.devclient.test:color/primary_text_default_material_dark = 0x7f0500c3
expo.modules.devclient.test:attr/checkboxStyle = 0x7f030082
expo.modules.devclient.test:attr/backgroundInsetEnd = 0x7f030042
expo.modules.devclient.test:attr/fontProviderPackage = 0x7f030134
expo.modules.devclient.test:attr/tabMode = 0x7f030255
expo.modules.devclient.test:attr/tabIndicatorGravity = 0x7f030250
expo.modules.devclient.test:color/design_snackbar_background_color = 0x7f05005b
expo.modules.devclient.test:attr/tabIndicator = 0x7f03024c
expo.modules.devclient.test:attr/tabIconTintMode = 0x7f03024b
expo.modules.devclient.test:attr/tabIconTint = 0x7f03024a
expo.modules.devclient.test:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f06010e
expo.modules.devclient.test:attr/tabGravity = 0x7f030249
expo.modules.devclient.test:attr/trackColor = 0x7f0302a3
expo.modules.devclient.test:attr/autoSizeMinTextSize = 0x7f030035
expo.modules.devclient.test:attr/switchTextAppearance = 0x7f030246
expo.modules.devclient.test:attr/switchStyle = 0x7f030245
expo.modules.devclient.test:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f0f028b
expo.modules.devclient.test:color/background_floating_material_light = 0x7f05001e
expo.modules.devclient.test:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f030242
expo.modules.devclient.test:attr/statusBarForeground = 0x7f030234
expo.modules.devclient.test:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
expo.modules.devclient.test:attr/suggestionRowLayout = 0x7f030241
expo.modules.devclient.test:color/design_default_color_on_error = 0x7f050048
expo.modules.devclient.test:attr/listPopupWindowStyle = 0x7f030196
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f0f02c2
expo.modules.devclient.test:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f006b
expo.modules.devclient.test:layout/browser_actions_context_menu_row = 0x7f0b0020
expo.modules.devclient.test:attr/suffixText = 0x7f03023e
expo.modules.devclient.test:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f0144
expo.modules.devclient.test:string/call_notification_hang_up_action = 0x7f0e0022
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_infoiconlight = 0x7f070028
expo.modules.devclient.test:attr/contentInsetEndWithActions = 0x7f0300c3
expo.modules.devclient.test:dimen/cardview_default_radius = 0x7f060058
expo.modules.devclient.test:color/mtrl_error = 0x7f0500a2
expo.modules.devclient.test:color/cardview_shadow_start_color = 0x7f050032
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Button = 0x7f0f0018
expo.modules.devclient.test:color/abc_decor_view_status_guard_light = 0x7f050006
expo.modules.devclient.test:attr/counterTextColor = 0x7f0300e0
expo.modules.devclient.test:attr/checkMarkTint = 0x7f030080
expo.modules.devclient.test:attr/subtitleTextStyle = 0x7f03023d
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f0f0124
expo.modules.devclient.test:attr/floatingActionButtonStyle = 0x7f03012d
expo.modules.devclient.test:drawable/ic_call_answer_video_low = 0x7f0700a9
expo.modules.devclient.test:id/pin = 0x7f0800f4
expo.modules.devclient.test:dimen/mtrl_btn_text_size = 0x7f0600c8
expo.modules.devclient.test:attr/showText = 0x7f030217
expo.modules.devclient.test:string/mtrl_picker_save = 0x7f0e0076
expo.modules.devclient.test:layout/select_dialog_item_material = 0x7f0b005f
expo.modules.devclient.test:attr/subtitle = 0x7f03023a
expo.modules.devclient.test:attr/endIconCheckable = 0x7f030102
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f0f005d
expo.modules.devclient.test:attr/strokeWidth = 0x7f030237
expo.modules.devclient.test:styleable/MaterialShape = 0x7f100040
expo.modules.devclient.test:id/accessibility_custom_action_9 = 0x7f08002d
expo.modules.devclient.test:attr/state_collapsed = 0x7f03022e
expo.modules.devclient.test:dimen/compat_control_corner_material = 0x7f06005d
expo.modules.devclient.test:attr/windowMinWidthMinor = 0x7f0302ba
expo.modules.devclient.test:id/forever = 0x7f0800a5
expo.modules.devclient.test:color/design_default_color_secondary_variant = 0x7f050050
expo.modules.devclient.test:anim/design_bottom_sheet_slide_in = 0x7f01001e
expo.modules.devclient.test:attr/actionBarDivider = 0x7f030000
expo.modules.devclient.test:attr/tickMark = 0x7f03028b
expo.modules.devclient.test:string/abc_searchview_description_query = 0x7f0e0014
expo.modules.devclient.test:attr/startIconContentDescription = 0x7f030229
expo.modules.devclient.test:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f00ae
expo.modules.devclient.test:drawable/abc_spinner_mtrl_am_alpha = 0x7f070081
expo.modules.devclient.test:attr/itemTextAppearance = 0x7f030175
expo.modules.devclient.test:dimen/mtrl_slider_label_padding = 0x7f060128
expo.modules.devclient.test:dimen/design_fab_image_size = 0x7f060072
expo.modules.devclient.test:attr/failureImage = 0x7f030125
expo.modules.devclient.test:attr/startIconCheckable = 0x7f030228
expo.modules.devclient.test:dimen/abc_dialog_corner_radius_material = 0x7f06001b
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Headline5 = 0x7f0f0180
expo.modules.devclient.test:anim/catalyst_fade_out = 0x7f010019
expo.modules.devclient.test:dimen/abc_config_prefDialogWidth = 0x7f060017
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0237
expo.modules.devclient.test:style/Base.V21.Theme.AppCompat.Light = 0x7f0f0084
expo.modules.devclient.test:attr/sliderStyle = 0x7f03021d
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_warningtriangleicon = 0x7f070040
expo.modules.devclient.test:color/design_default_color_background = 0x7f050045
expo.modules.devclient.test:attr/fabAlignmentMode = 0x7f03011d
expo.modules.devclient.test:attr/chipStrokeWidth = 0x7f03009b
expo.modules.devclient.test:drawable/abc_text_cursor_material = 0x7f070089
expo.modules.devclient.test:attr/layout_scrollInterpolator = 0x7f03018a
expo.modules.devclient.test:attr/panelMenuListTheme = 0x7f0301ce
expo.modules.devclient.test:attr/switchMinWidth = 0x7f030243
expo.modules.devclient.test:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
expo.modules.devclient.test:attr/showDividers = 0x7f030215
expo.modules.devclient.test:attr/actionBarItemBackground = 0x7f030001
expo.modules.devclient.test:attr/showAsAction = 0x7f030214
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f0f00f0
expo.modules.devclient.test:id/accessibility_custom_action_0 = 0x7f08000e
expo.modules.devclient.test:attr/cornerSizeTopLeft = 0x7f0300d9
expo.modules.devclient.test:attr/shortcutMatchRequired = 0x7f030213
expo.modules.devclient.test:attr/enforceTextAppearance = 0x7f030109
expo.modules.devclient.test:attr/strokeColor = 0x7f030236
expo.modules.devclient.test:attr/colorOnSecondary = 0x7f0300b6
expo.modules.devclient.test:attr/colorSwitchThumbNormal = 0x7f0300bf
expo.modules.devclient.test:id/center_vertical = 0x7f080065
expo.modules.devclient.test:attr/shapeAppearanceSmallComponent = 0x7f030212
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f001b
expo.modules.devclient.test:attr/shapeAppearanceOverlay = 0x7f030211
expo.modules.devclient.test:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070061
expo.modules.devclient.test:attr/shapeAppearanceMediumComponent = 0x7f030210
expo.modules.devclient.test:dimen/material_text_view_test_line_height = 0x7f0600a1
expo.modules.devclient.test:attr/selectableItemBackground = 0x7f03020b
expo.modules.devclient.test:attr/iconPadding = 0x7f030154
expo.modules.devclient.test:attr/checkedIconEnabled = 0x7f030086
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f0f00d8
expo.modules.devclient.test:attr/backgroundSplit = 0x7f030046
expo.modules.devclient.test:attr/seekBarStyle = 0x7f03020a
expo.modules.devclient.test:drawable/abc_list_divider_material = 0x7f070068
expo.modules.devclient.test:animator/design_fab_show_motion_spec = 0x7f020002
expo.modules.devclient.test:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f060136
expo.modules.devclient.test:color/abc_secondary_text_material_dark = 0x7f050011
expo.modules.devclient.test:dimen/mtrl_calendar_dialog_background_inset = 0x7f0600d5
expo.modules.devclient.test:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f00cf
expo.modules.devclient.test:id/transition_layout_save = 0x7f08015f
expo.modules.devclient.test:attr/thumbTintMode = 0x7f030287
expo.modules.devclient.test:style/Widget.MaterialComponents.Snackbar = 0x7f0f02ba
expo.modules.devclient.test:style/Base.V26.Theme.AppCompat = 0x7f0f008f
expo.modules.devclient.test:id/rn_redbox_loading_indicator = 0x7f080103
expo.modules.devclient.test:id/message = 0x7f0800c4
expo.modules.devclient.test:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f0600d7
expo.modules.devclient.test:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
expo.modules.devclient.test:attr/listChoiceIndicatorMultipleAnimated = 0x7f030190
expo.modules.devclient.test:attr/textAppearanceSearchResultSubtitle = 0x7f030273
expo.modules.devclient.test:dimen/mtrl_btn_icon_padding = 0x7f0600bc
expo.modules.devclient.test:styleable/FontFamilyFont = 0x7f10002b
expo.modules.devclient.test:attr/maxActionInlineWidth = 0x7f0301b4
expo.modules.devclient.test:interpolator/mtrl_fast_out_slow_in = 0x7f0a0008
expo.modules.devclient.test:attr/roundingBorderWidth = 0x7f030203
expo.modules.devclient.test:attr/roundingBorderPadding = 0x7f030202
expo.modules.devclient.test:dimen/mtrl_fab_min_touch_target = 0x7f060111
expo.modules.devclient.test:attr/backgroundOverlayColorAlpha = 0x7f030045
expo.modules.devclient.test:id/textEnd = 0x7f080149
expo.modules.devclient.test:attr/roundingBorderColor = 0x7f030201
expo.modules.devclient.test:animator/mtrl_card_state_list_anim = 0x7f02000b
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f023a
expo.modules.devclient.test:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f0f0073
expo.modules.devclient.test:attr/itemTextAppearanceInactive = 0x7f030177
expo.modules.devclient.test:attr/roundTopStart = 0x7f0301fe
expo.modules.devclient.test:dimen/mtrl_min_touch_target_size = 0x7f06011d
expo.modules.devclient.test:attr/roundTopRight = 0x7f0301fd
expo.modules.devclient.test:dimen/mtrl_high_ripple_focused_alpha = 0x7f060115
expo.modules.devclient.test:id/design_menu_item_action_area = 0x7f080080
expo.modules.devclient.test:color/abc_primary_text_disable_only_material_light = 0x7f05000a
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f0f0125
expo.modules.devclient.test:attr/roundTopLeft = 0x7f0301fc
expo.modules.devclient.test:style/Widget.AppCompat.Button = 0x7f0f0221
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f0f01da
expo.modules.devclient.test:attr/values = 0x7f0302ad
expo.modules.devclient.test:attr/tabMinWidth = 0x7f030254
expo.modules.devclient.test:attr/chipSurfaceColor = 0x7f03009d
expo.modules.devclient.test:attr/roundBottomRight = 0x7f0301f9
expo.modules.devclient.test:attr/roundBottomLeft = 0x7f0301f8
expo.modules.devclient.test:string/catalyst_hot_reloading_auto_enable = 0x7f0e0032
expo.modules.devclient.test:id/mtrl_calendar_frame = 0x7f0800d0
expo.modules.devclient.test:dimen/design_navigation_item_horizontal_padding = 0x7f06007a
expo.modules.devclient.test:attr/colorOnSurface = 0x7f0300b7
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f0f01bb
expo.modules.devclient.test:attr/roundBottomEnd = 0x7f0301f7
expo.modules.devclient.test:id/accessibility_custom_action_15 = 0x7f080015
expo.modules.devclient.test:attr/tickColorInactive = 0x7f03028a
expo.modules.devclient.test:attr/reverseLayout = 0x7f0301f4
expo.modules.devclient.test:dimen/mtrl_extended_fab_min_width = 0x7f060109
expo.modules.devclient.test:layout/abc_dialog_title_material = 0x7f0b000c
expo.modules.devclient.test:attr/recyclerViewStyle = 0x7f0301f1
expo.modules.devclient.test:attr/actionBarWidgetTheme = 0x7f03000a
expo.modules.devclient.test:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0053
expo.modules.devclient.test:attr/drawableTint = 0x7f0300f5
expo.modules.devclient.test:attr/ratingBarStyleIndicator = 0x7f0301ef
expo.modules.devclient.test:string/fallback_menu_item_copy_link = 0x7f0e004f
expo.modules.devclient.test:attr/allowStacking = 0x7f03002c
expo.modules.devclient.test:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f0f01e3
expo.modules.devclient.test:style/Theme.AppCompat.Light = 0x7f0f0198
expo.modules.devclient.test:attr/radioButtonStyle = 0x7f0301ec
expo.modules.devclient.test:styleable/Chip = 0x7f10001c
expo.modules.devclient.test:attr/queryPatterns = 0x7f0301eb
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Light.BottomSheetDialog = 0x7f0f0201
expo.modules.devclient.test:style/TextAppearance.AppCompat.Inverse = 0x7f0f0140
expo.modules.devclient.test:color/bright_foreground_inverse_material_dark = 0x7f050023
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Dialog = 0x7f0f005a
expo.modules.devclient.test:attr/autoCompleteTextViewStyle = 0x7f030033
expo.modules.devclient.test:attr/queryBackground = 0x7f0301e9
expo.modules.devclient.test:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
expo.modules.devclient.test:attr/snackbarButtonStyle = 0x7f03021e
expo.modules.devclient.test:attr/colorButtonNormal = 0x7f0300ad
expo.modules.devclient.test:style/Theme.Design.Light = 0x7f0f01a7
expo.modules.devclient.test:drawable/abc_edit_text_material = 0x7f070058
expo.modules.devclient.test:attr/multiChoiceItemLayout = 0x7f0301bc
expo.modules.devclient.test:attr/progressBarPadding = 0x7f0301e7
expo.modules.devclient.test:attr/roundedCornerRadius = 0x7f030200
expo.modules.devclient.test:attr/progressBarImageScaleType = 0x7f0301e6
expo.modules.devclient.test:dimen/mtrl_navigation_elevation = 0x7f06011e
expo.modules.devclient.test:dimen/material_emphasis_medium = 0x7f0600a0
expo.modules.devclient.test:color/bright_foreground_disabled_material_dark = 0x7f050021
expo.modules.devclient.test:string/fab_transformation_sheet_behavior = 0x7f0e004e
expo.modules.devclient.test:attr/progressBarImage = 0x7f0301e5
expo.modules.devclient.test:attr/toolbarId = 0x7f03029b
expo.modules.devclient.test:dimen/abc_action_button_min_width_material = 0x7f06000e
expo.modules.devclient.test:style/ShapeAppearanceOverlay.TopRightDifferentCornerSize = 0x7f0f0128
expo.modules.devclient.test:style/Base.Widget.AppCompat.Button.Small = 0x7f0f00ac
expo.modules.devclient.test:attr/materialButtonOutlinedStyle = 0x7f0301a5
expo.modules.devclient.test:attr/pressedStateOverlayImage = 0x7f0301e2
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f0238
expo.modules.devclient.test:layout/mtrl_calendar_month = 0x7f0b0043
expo.modules.devclient.test:dimen/fastscroll_default_thickness = 0x7f060091
expo.modules.devclient.test:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f010d
expo.modules.devclient.test:attr/useCompatPadding = 0x7f0302ab
expo.modules.devclient.test:attr/roundWithOverlayColor = 0x7f0301ff
expo.modules.devclient.test:attr/prefixTextColor = 0x7f0301e0
expo.modules.devclient.test:attr/prefixTextAppearance = 0x7f0301df
expo.modules.devclient.test:attr/scrimAnimationDuration = 0x7f030204
expo.modules.devclient.test:attr/prefixText = 0x7f0301de
expo.modules.devclient.test:id/mtrl_motion_snapshot_view = 0x7f0800d9
expo.modules.devclient.test:attr/materialAlertDialogTitleTextStyle = 0x7f0301a4
expo.modules.devclient.test:id/up = 0x7f080166
expo.modules.devclient.test:attr/popupWindowStyle = 0x7f0301dd
expo.modules.devclient.test:color/abc_tint_spinner = 0x7f050017
expo.modules.devclient.test:attr/behavior_saveFlags = 0x7f030057
expo.modules.devclient.test:attr/popupTheme = 0x7f0301dc
expo.modules.devclient.test:attr/haloColor = 0x7f03013e
expo.modules.devclient.test:color/mtrl_outlined_stroke_color = 0x7f0500af
expo.modules.devclient.test:attr/cornerSize = 0x7f0300d6
expo.modules.devclient.test:attr/popupMenuBackground = 0x7f0301da
expo.modules.devclient.test:attr/passwordToggleDrawable = 0x7f0301d1
expo.modules.devclient.test:dimen/design_tab_scrollable_min_width = 0x7f06008b
expo.modules.devclient.test:styleable/AnimatedStateListDrawableTransition = 0x7f100009
expo.modules.devclient.test:attr/actionModeCopyDrawable = 0x7f030014
expo.modules.devclient.test:attr/panelBackground = 0x7f0301cd
expo.modules.devclient.test:dimen/mtrl_btn_pressed_z = 0x7f0600c3
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat = 0x7f0f0015
expo.modules.devclient.test:anim/abc_slide_in_bottom = 0x7f010006
expo.modules.devclient.test:attr/paddingRightSystemWindowInsets = 0x7f0301ca
expo.modules.devclient.test:string/call_notification_answer_video_action = 0x7f0e0020
expo.modules.devclient.test:drawable/ic_call_answer_video = 0x7f0700a8
expo.modules.devclient.test:attr/paddingLeftSystemWindowInsets = 0x7f0301c9
expo.modules.devclient.test:dimen/design_fab_translation_z_hovered_focused = 0x7f060075
expo.modules.devclient.test:attr/queryHint = 0x7f0301ea
expo.modules.devclient.test:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0600dd
expo.modules.devclient.test:id/zero_corner_chip = 0x7f080176
expo.modules.devclient.test:attr/paddingEnd = 0x7f0301c8
expo.modules.devclient.test:id/tag_accessibility_pane_title = 0x7f080139
expo.modules.devclient.test:attr/actionTextColorAlpha = 0x7f030022
expo.modules.devclient.test:attr/endIconDrawable = 0x7f030104
expo.modules.devclient.test:anim/mtrl_card_lowers_interpolator = 0x7f010025
expo.modules.devclient.test:attr/itemRippleColor = 0x7f03016a
expo.modules.devclient.test:attr/layout_scrollFlags = 0x7f030189
expo.modules.devclient.test:attr/paddingBottomNoButtons = 0x7f0301c6
expo.modules.devclient.test:attr/number = 0x7f0301c2
expo.modules.devclient.test:attr/itemShapeAppearanceOverlay = 0x7f03016c
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0041
expo.modules.devclient.test:attr/startIconTint = 0x7f03022b
expo.modules.devclient.test:attr/preserveIconSpacing = 0x7f0301e1
expo.modules.devclient.test:attr/shapeAppearanceLargeComponent = 0x7f03020f
expo.modules.devclient.test:attr/overlayImage = 0x7f0301c5
expo.modules.devclient.test:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f0f0061
expo.modules.devclient.test:id/mtrl_calendar_months = 0x7f0800d2
expo.modules.devclient.test:dimen/design_navigation_padding_bottom = 0x7f06007d
expo.modules.devclient.test:style/Widget.MaterialComponents.FloatingActionButton = 0x7f0f029e
expo.modules.devclient.test:attr/numericModifiers = 0x7f0301c3
expo.modules.devclient.test:style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f0f012b
expo.modules.devclient.test:attr/tabContentStart = 0x7f030248
expo.modules.devclient.test:attr/actionDropDownStyle = 0x7f03000c
expo.modules.devclient.test:attr/navigationViewStyle = 0x7f0301c0
expo.modules.devclient.test:attr/background = 0x7f03003e
expo.modules.devclient.test:attr/ttcIndex = 0x7f0302aa
expo.modules.devclient.test:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_chevronright = 0x7f070008
expo.modules.devclient.test:dimen/hint_pressed_alpha_material_dark = 0x7f060099
expo.modules.devclient.test:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f0259
expo.modules.devclient.test:color/mtrl_textinput_filled_box_default_background_color = 0x7f0500ba
expo.modules.devclient.test:attr/navigationIcon = 0x7f0301be
expo.modules.devclient.test:id/actions = 0x7f080046
expo.modules.devclient.test:id/accessibility_custom_action_27 = 0x7f080022
expo.modules.devclient.test:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
expo.modules.devclient.test:attr/navigationContentDescription = 0x7f0301bd
expo.modules.devclient.test:attr/windowMinWidthMajor = 0x7f0302b9
expo.modules.devclient.test:attr/itemPadding = 0x7f030169
expo.modules.devclient.test:attr/fabCradleVerticalOffset = 0x7f030121
expo.modules.devclient.test:color/background_material_light = 0x7f050020
expo.modules.devclient.test:attr/actionBarStyle = 0x7f030005
expo.modules.devclient.test:attr/maxImageSize = 0x7f0301b7
expo.modules.devclient.test:attr/errorTextAppearance = 0x7f030110
expo.modules.devclient.test:layout/mtrl_picker_dialog = 0x7f0b004c
expo.modules.devclient.test:attr/closeIconVisible = 0x7f0300a4
expo.modules.devclient.test:attr/showTitle = 0x7f030218
expo.modules.devclient.test:attr/controlBackground = 0x7f0300ce
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.TextView = 0x7f0f00e1
expo.modules.devclient.test:attr/behavior_peekHeight = 0x7f030056
expo.modules.devclient.test:attr/maxCharacterCount = 0x7f0301b6
expo.modules.devclient.test:attr/contentPaddingRight = 0x7f0300cb
expo.modules.devclient.test:attr/materialThemeOverlay = 0x7f0301b3
expo.modules.devclient.test:dimen/design_bottom_sheet_modal_elevation = 0x7f06006e
expo.modules.devclient.test:string/bottom_sheet_behavior = 0x7f0e001e
expo.modules.devclient.test:attr/alertDialogTheme = 0x7f03002b
expo.modules.devclient.test:attr/spanCount = 0x7f030221
expo.modules.devclient.test:style/TextAppearance.Design.Suffix = 0x7f0f0174
expo.modules.devclient.test:style/CardView.Light = 0x7f0f00e6
expo.modules.devclient.test:id/SYM = 0x7f080007
expo.modules.devclient.test:attr/cardViewStyle = 0x7f03007e
expo.modules.devclient.test:attr/materialCalendarStyle = 0x7f0301b0
expo.modules.devclient.test:dimen/mtrl_calendar_month_vertical_padding = 0x7f0600e2
expo.modules.devclient.test:style/Widget.AppCompat.Spinner = 0x7f0f0257
expo.modules.devclient.test:attr/tint = 0x7f03028e
expo.modules.devclient.test:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f008a
expo.modules.devclient.test:attr/materialCalendarHeaderDivider = 0x7f0301ab
expo.modules.devclient.test:dimen/notification_big_circle_margin = 0x7f060148
expo.modules.devclient.test:color/button_material_light = 0x7f05002c
expo.modules.devclient.test:attr/collapsedTitleTextAppearance = 0x7f0300a9
expo.modules.devclient.test:color/abc_primary_text_material_light = 0x7f05000c
expo.modules.devclient.test:dimen/tooltip_y_offset_non_touch = 0x7f06015c
expo.modules.devclient.test:attr/suffixTextAppearance = 0x7f03023f
expo.modules.devclient.test:string/character_counter_overflowed_content_description = 0x7f0e0041
expo.modules.devclient.test:dimen/abc_star_small = 0x7f06003d
expo.modules.devclient.test:dimen/design_tab_max_width = 0x7f06008a
expo.modules.devclient.test:animator/design_fab_hide_motion_spec = 0x7f020001
expo.modules.devclient.test:attr/titleTextColor = 0x7f030299
expo.modules.devclient.test:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f0f029d
expo.modules.devclient.test:attr/daySelectedStyle = 0x7f0300e3
expo.modules.devclient.test:attr/logoDescription = 0x7f03019f
expo.modules.devclient.test:attr/buttonBarStyle = 0x7f03006e
expo.modules.devclient.test:dimen/mtrl_chip_text_size = 0x7f0600fa
expo.modules.devclient.test:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f0f00ef
expo.modules.devclient.test:attr/thumbRadius = 0x7f030284
expo.modules.devclient.test:attr/listPreferredItemPaddingStart = 0x7f03019d
expo.modules.devclient.test:attr/statusBarScrim = 0x7f030235
expo.modules.devclient.test:attr/listMenuViewStyle = 0x7f030195
expo.modules.devclient.test:style/Widget.MaterialComponents.CardView = 0x7f0f0292
expo.modules.devclient.test:dimen/notification_action_text_size = 0x7f060147
expo.modules.devclient.test:dimen/design_appbar_elevation = 0x7f060061
expo.modules.devclient.test:attr/listLayout = 0x7f030194
expo.modules.devclient.test:layout/design_navigation_item_separator = 0x7f0b002b
expo.modules.devclient.test:attr/paddingStart = 0x7f0301cb
expo.modules.devclient.test:style/Widget.Support.CoordinatorLayout = 0x7f0f02d2
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0231
expo.modules.devclient.test:color/design_default_color_error = 0x7f050046
expo.modules.devclient.test:attr/listChoiceIndicatorSingleAnimated = 0x7f030191
expo.modules.devclient.test:drawable/abc_star_black_48dp = 0x7f070083
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f0f020f
expo.modules.devclient.test:anim/catalyst_push_up_out = 0x7f01001b
expo.modules.devclient.test:dimen/design_snackbar_action_text_color_alpha = 0x7f060080
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f0f0206
expo.modules.devclient.test:attr/tabIndicatorAnimationDuration = 0x7f03024d
expo.modules.devclient.test:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f0f01cc
expo.modules.devclient.test:dimen/mtrl_card_elevation = 0x7f0600f7
expo.modules.devclient.test:attr/placeholderText = 0x7f0301d7
expo.modules.devclient.test:dimen/design_tab_text_size = 0x7f06008c
expo.modules.devclient.test:style/Theme.AppCompat.Dialog.Alert = 0x7f0f0194
expo.modules.devclient.test:attr/materialCardViewStyle = 0x7f0301b2
expo.modules.devclient.test:attr/showMotionSpec = 0x7f030216
expo.modules.devclient.test:attr/insetForeground = 0x7f03015d
expo.modules.devclient.test:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0600de
expo.modules.devclient.test:attr/windowFixedHeightMinor = 0x7f0302b6
expo.modules.devclient.test:attr/layout_dodgeInsetEdges = 0x7f030186
expo.modules.devclient.test:id/view_tree_saved_state_registry_owner = 0x7f08016f
expo.modules.devclient.test:attr/badgeTextColor = 0x7f03004c
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_homefilledinactiveiconlight = 0x7f070026
expo.modules.devclient.test:attr/layout_collapseMode = 0x7f030184
expo.modules.devclient.test:attr/layoutManager = 0x7f030180
expo.modules.devclient.test:id/mtrl_calendar_days_of_week = 0x7f0800cf
expo.modules.devclient.test:attr/icon = 0x7f030151
expo.modules.devclient.test:attr/textAppearanceListItemSmall = 0x7f030270
expo.modules.devclient.test:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f0190
expo.modules.devclient.test:id/BOTTOM_START = 0x7f080002
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f0f02b0
expo.modules.devclient.test:id/tag_unhandled_key_event_manager = 0x7f080140
expo.modules.devclient.test:attr/lastBaselineToBottomHeight = 0x7f03017e
expo.modules.devclient.test:attr/dialogCornerRadius = 0x7f0300e7
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f0f01c2
expo.modules.devclient.test:attr/tabTextAppearance = 0x7f03025e
expo.modules.devclient.test:id/touch_outside = 0x7f08015b
expo.modules.devclient.test:attr/itemTextColor = 0x7f030178
expo.modules.devclient.test:drawable/abc_list_pressed_holo_light = 0x7f07006d
expo.modules.devclient.test:color/material_on_primary_disabled = 0x7f05007d
expo.modules.devclient.test:id/accessibility_custom_action_21 = 0x7f08001c
expo.modules.devclient.test:dimen/abc_edit_text_inset_top_material = 0x7f06002e
expo.modules.devclient.test:attr/textAppearanceLineHeightEnabled = 0x7f03026d
expo.modules.devclient.test:color/material_slider_inactive_tick_marks_color = 0x7f050087
expo.modules.devclient.test:id/autofill_inline_suggestion_start_icon = 0x7f080050
expo.modules.devclient.test:dimen/mtrl_slider_widget_height = 0x7f060130
expo.modules.devclient.test:attr/itemIconTint = 0x7f030167
expo.modules.devclient.test:id/accessibility_custom_action_12 = 0x7f080012
expo.modules.devclient.test:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f0f0071
expo.modules.devclient.test:dimen/design_bottom_navigation_elevation = 0x7f060065
expo.modules.devclient.test:attr/errorIconTint = 0x7f03010e
expo.modules.devclient.test:attr/materialButtonToggleGroupStyle = 0x7f0301a7
expo.modules.devclient.test:attr/itemStrokeColor = 0x7f030173
expo.modules.devclient.test:attr/itemShapeInsetTop = 0x7f030171
expo.modules.devclient.test:string/clear_text_end_icon_content_description = 0x7f0e0044
expo.modules.devclient.test:attr/endIconContentDescription = 0x7f030103
expo.modules.devclient.test:attr/textInputLayoutFocusedRectEnabled = 0x7f03027b
expo.modules.devclient.test:attr/itemShapeInsetBottom = 0x7f03016e
expo.modules.devclient.test:id/accessibility_custom_action_3 = 0x7f080025
expo.modules.devclient.test:attr/pressedTranslationZ = 0x7f0301e3
expo.modules.devclient.test:string/abc_menu_meta_shortcut_label = 0x7f0e000d
expo.modules.devclient.test:id/accessibility_custom_action_18 = 0x7f080018
expo.modules.devclient.test:id/mini = 0x7f0800c6
expo.modules.devclient.test:attr/buttonIconDimen = 0x7f030071
expo.modules.devclient.test:style/Widget.AppCompat.ActionBar = 0x7f0f0216
expo.modules.devclient.test:color/material_grey_100 = 0x7f050073
expo.modules.devclient.test:id/exitUntilCollapsed = 0x7f080093
expo.modules.devclient.test:attr/itemIconPadding = 0x7f030165
expo.modules.devclient.test:color/abc_background_cache_hint_selector_material_light = 0x7f050001
expo.modules.devclient.test:id/accessibility_custom_action_17 = 0x7f080017
expo.modules.devclient.test:attr/contentPaddingLeft = 0x7f0300ca
expo.modules.devclient.test:color/material_deep_teal_500 = 0x7f050072
expo.modules.devclient.test:attr/actionViewClass = 0x7f030023
expo.modules.devclient.test:id/action_bar = 0x7f080035
expo.modules.devclient.test:color/accent_material_light = 0x7f05001a
expo.modules.devclient.test:attr/tickColorActive = 0x7f030289
expo.modules.devclient.test:id/tag_screen_reader_focusable = 0x7f08013d
expo.modules.devclient.test:attr/actualImageResource = 0x7f030025
expo.modules.devclient.test:drawable/avd_hide_password = 0x7f070094
expo.modules.devclient.test:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f0199
expo.modules.devclient.test:color/mtrl_card_view_ripple = 0x7f050099
expo.modules.devclient.test:attr/actionModeCloseDrawable = 0x7f030013
expo.modules.devclient.test:color/material_grey_300 = 0x7f050074
expo.modules.devclient.test:layout/alert_title_layout = 0x7f0b001c
expo.modules.devclient.test:attr/itemHorizontalTranslationEnabled = 0x7f030164
expo.modules.devclient.test:attr/placeholderTextAppearance = 0x7f0301d8
expo.modules.devclient.test:dimen/autofill_inline_suggestion_icon_size = 0x7f060053
expo.modules.devclient.test:style/Theme.MaterialComponents.CompactMenu = 0x7f0f01b3
expo.modules.devclient.test:id/checked = 0x7f080067
expo.modules.devclient.test:attr/maxLines = 0x7f0301b8
expo.modules.devclient.test:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f019e
expo.modules.devclient.test:attr/windowActionModeOverlay = 0x7f0302b4
expo.modules.devclient.test:attr/materialAlertDialogTheme = 0x7f0301a1
expo.modules.devclient.test:attr/indeterminateProgressStyle = 0x7f03015b
expo.modules.devclient.test:attr/iconifiedByDefault = 0x7f030159
expo.modules.devclient.test:styleable/AppCompatImageView = 0x7f10000e
expo.modules.devclient.test:attr/iconTintMode = 0x7f030158
expo.modules.devclient.test:attr/textAppearanceHeadline3 = 0x7f030268
expo.modules.devclient.test:id/chip2 = 0x7f08006a
expo.modules.devclient.test:attr/iconGravity = 0x7f030153
expo.modules.devclient.test:string/state_unselected_description = 0x7f0e0093
expo.modules.devclient.test:attr/checkedChip = 0x7f030084
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f015f
expo.modules.devclient.test:attr/iconEndPadding = 0x7f030152
expo.modules.devclient.test:style/TestStyleWithoutLineHeight = 0x7f0f0133
expo.modules.devclient.test:attr/labelBehavior = 0x7f03017b
expo.modules.devclient.test:attr/hoveredFocusedTranslationZ = 0x7f030150
expo.modules.devclient.test:id/accessibility_custom_action_16 = 0x7f080016
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomNavigationView = 0x7f0f0281
expo.modules.devclient.test:string/rn_tab_description = 0x7f0e0088
expo.modules.devclient.test:attr/hintTextAppearance = 0x7f03014b
expo.modules.devclient.test:dimen/abc_star_medium = 0x7f06003c
expo.modules.devclient.test:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f0f0077
expo.modules.devclient.test:attr/hintAnimationEnabled = 0x7f030149
expo.modules.devclient.test:id/search_button = 0x7f080116
expo.modules.devclient.test:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0600fe
expo.modules.devclient.test:attr/tabPadding = 0x7f030256
expo.modules.devclient.test:attr/hideOnScroll = 0x7f030148
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f0f01d5
expo.modules.devclient.test:attr/toolbarNavigationButtonStyle = 0x7f03029c
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f0f020a
expo.modules.devclient.test:attr/hideOnContentScroll = 0x7f030147
expo.modules.devclient.test:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f00a6
expo.modules.devclient.test:attr/helperText = 0x7f030142
expo.modules.devclient.test:dimen/abc_dropdownitem_icon_width = 0x7f060029
expo.modules.devclient.test:color/catalyst_redbox_background = 0x7f050034
expo.modules.devclient.test:id/uniform = 0x7f080164
expo.modules.devclient.test:attr/goIcon = 0x7f03013d
expo.modules.devclient.test:attr/contentPaddingBottom = 0x7f0300c9
expo.modules.devclient.test:attr/gapBetweenBars = 0x7f03013b
expo.modules.devclient.test:color/abc_tint_btn_checkable = 0x7f050013
expo.modules.devclient.test:attr/checkedIcon = 0x7f030085
expo.modules.devclient.test:attr/checkedButton = 0x7f030083
expo.modules.devclient.test:attr/fontWeight = 0x7f030139
expo.modules.devclient.test:attr/helperTextEnabled = 0x7f030143
expo.modules.devclient.test:attr/fontProviderAuthority = 0x7f030130
expo.modules.devclient.test:attr/titleMarginBottom = 0x7f030293
expo.modules.devclient.test:attr/animationMode = 0x7f03002f
expo.modules.devclient.test:id/accessibility_custom_action_2 = 0x7f08001a
expo.modules.devclient.test:color/mtrl_tabs_legacy_text_color_selector = 0x7f0500b5
expo.modules.devclient.test:attr/helperTextTextColor = 0x7f030145
expo.modules.devclient.test:attr/buttonBarPositiveButtonStyle = 0x7f03006d
expo.modules.devclient.test:attr/headerLayout = 0x7f030140
expo.modules.devclient.test:id/edit_text_id = 0x7f080088
expo.modules.devclient.test:drawable/notification_bg_normal_pressed = 0x7f0700c8
expo.modules.devclient.test:attr/collapseContentDescription = 0x7f0300a6
expo.modules.devclient.test:attr/layout_insetEdge = 0x7f030187
expo.modules.devclient.test:style/Base.Animation.AppCompat.Tooltip = 0x7f0f000e
expo.modules.devclient.test:attr/closeIconTint = 0x7f0300a3
expo.modules.devclient.test:attr/colorOnPrimarySurface = 0x7f0300b5
expo.modules.devclient.test:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
expo.modules.devclient.test:attr/expanded = 0x7f030113
expo.modules.devclient.test:id/accessibility_custom_action_24 = 0x7f08001f
expo.modules.devclient.test:attr/fabAnimationMode = 0x7f03011e
expo.modules.devclient.test:dimen/abc_dialog_min_width_major = 0x7f060022
expo.modules.devclient.test:attr/itemMaxLines = 0x7f030168
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f0f02c3
expo.modules.devclient.test:color/design_dark_default_color_error = 0x7f050039
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f0f02c9
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f0f0203
expo.modules.devclient.test:layout/abc_action_menu_layout = 0x7f0b0003
expo.modules.devclient.test:attr/drawableRightCompat = 0x7f0300f2
expo.modules.devclient.test:attr/autofillInlineSuggestionStartIconStyle = 0x7f03003b
expo.modules.devclient.test:attr/extendedFloatingActionButtonStyle = 0x7f03011c
expo.modules.devclient.test:color/bright_foreground_disabled_material_light = 0x7f050022
expo.modules.devclient.test:attr/expandActivityOverflowButtonDrawable = 0x7f030112
expo.modules.devclient.test:attr/boxCollapsedPaddingTop = 0x7f030061
expo.modules.devclient.test:attr/liftOnScrollTargetViewId = 0x7f03018c
expo.modules.devclient.test:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
expo.modules.devclient.test:attr/endIconTintMode = 0x7f030107
expo.modules.devclient.test:attr/fontProviderFetchTimeout = 0x7f030133
expo.modules.devclient.test:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f0f0013
expo.modules.devclient.test:layout/text_view_with_line_height_from_appearance = 0x7f0b006c
expo.modules.devclient.test:attr/homeLayout = 0x7f03014e
expo.modules.devclient.test:attr/state_collapsible = 0x7f03022f
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f0f0122
expo.modules.devclient.test:dimen/mtrl_calendar_action_padding = 0x7f0600cb
expo.modules.devclient.test:attr/cardBackgroundColor = 0x7f030077
expo.modules.devclient.test:layout/notification_template_part_chronometer = 0x7f0b0059
expo.modules.devclient.test:attr/contentInsetStart = 0x7f0300c6
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0031
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f001c
expo.modules.devclient.test:animator/fragment_fade_exit = 0x7f020006
expo.modules.devclient.test:drawable/abc_btn_radio_material = 0x7f07004d
expo.modules.devclient.test:layout/abc_list_menu_item_icon = 0x7f0b000f
expo.modules.devclient.test:attr/iconSize = 0x7f030155
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f0f011f
expo.modules.devclient.test:attr/drawableBottomCompat = 0x7f0300ef
expo.modules.devclient.test:attr/boxStrokeColor = 0x7f030066
expo.modules.devclient.test:dimen/mtrl_badge_text_size = 0x7f0600ac
expo.modules.devclient.test:attr/errorEnabled = 0x7f03010c
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f001e
expo.modules.devclient.test:attr/tabIndicatorHeight = 0x7f030251
expo.modules.devclient.test:color/mtrl_choice_chip_text_color = 0x7f0500a1
expo.modules.devclient.test:attr/endIconTint = 0x7f030106
expo.modules.devclient.test:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f00c3
expo.modules.devclient.test:dimen/mtrl_extended_fab_end_padding_icon = 0x7f060105
expo.modules.devclient.test:attr/endIconMode = 0x7f030105
expo.modules.devclient.test:attr/behavior_hideable = 0x7f030054
expo.modules.devclient.test:attr/textAppearanceCaption = 0x7f030265
expo.modules.devclient.test:attr/elevationOverlayEnabled = 0x7f030100
expo.modules.devclient.test:id/password_toggle = 0x7f0800f2
expo.modules.devclient.test:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f0035
expo.modules.devclient.test:id/textinput_error = 0x7f080150
expo.modules.devclient.test:attr/editTextBackground = 0x7f0300fb
expo.modules.devclient.test:attr/expandedTitleMarginEnd = 0x7f030117
expo.modules.devclient.test:attr/textAllCaps = 0x7f030261
expo.modules.devclient.test:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f0f0273
expo.modules.devclient.test:layout/mtrl_picker_header_toggle = 0x7f0b0052
expo.modules.devclient.test:layout/error_console_list_item = 0x7f0b0034
expo.modules.devclient.test:interpolator/mtrl_fast_out_linear_in = 0x7f0a0007
expo.modules.devclient.test:attr/chipSpacingVertical = 0x7f030097
expo.modules.devclient.test:id/all = 0x7f08004b
expo.modules.devclient.test:attr/boxCornerRadiusBottomEnd = 0x7f030062
expo.modules.devclient.test:attr/drawableTintMode = 0x7f0300f6
expo.modules.devclient.test:style/Widget.AppCompat.RatingBar = 0x7f0f0250
expo.modules.devclient.test:attr/haloRadius = 0x7f03013f
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_usericon = 0x7f07003e
expo.modules.devclient.test:attr/alpha = 0x7f03002d
expo.modules.devclient.test:attr/autofillInlineSuggestionTitle = 0x7f03003d
expo.modules.devclient.test:attr/listChoiceBackgroundIndicator = 0x7f03018f
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_threefingerlongpressicon = 0x7f070039
expo.modules.devclient.test:style/NoAnimationDialog = 0x7f0f00f4
expo.modules.devclient.test:attr/actualImageUri = 0x7f030027
expo.modules.devclient.test:attr/ratingBarStyle = 0x7f0301ee
expo.modules.devclient.test:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f0011
expo.modules.devclient.test:attr/errorTextColor = 0x7f030111
expo.modules.devclient.test:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f009b
expo.modules.devclient.test:attr/homeAsUpIndicator = 0x7f03014d
expo.modules.devclient.test:attr/drawableSize = 0x7f0300f3
expo.modules.devclient.test:color/abc_tint_seek_thumb = 0x7f050016
expo.modules.devclient.test:id/accessibility_custom_action_28 = 0x7f080023
expo.modules.devclient.test:attr/colorPrimaryDark = 0x7f0300b9
expo.modules.devclient.test:attr/drawableLeftCompat = 0x7f0300f1
expo.modules.devclient.test:dimen/compat_button_padding_horizontal_material = 0x7f06005b
expo.modules.devclient.test:attr/horizontalOffset = 0x7f03014f
expo.modules.devclient.test:dimen/action_bar_size = 0x7f060051
expo.modules.devclient.test:attr/actionButtonStyle = 0x7f03000b
expo.modules.devclient.test:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0243
expo.modules.devclient.test:attr/logo = 0x7f03019e
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f0f01f1
expo.modules.devclient.test:dimen/mtrl_extended_fab_top_padding = 0x7f06010c
expo.modules.devclient.test:attr/initialActivityCount = 0x7f03015c
expo.modules.devclient.test:dimen/mtrl_btn_corner_radius = 0x7f0600b4
expo.modules.devclient.test:animator/mtrl_extended_fab_change_size_motion_spec = 0x7f02000d
expo.modules.devclient.test:color/mtrl_chip_ripple_color = 0x7f05009c
expo.modules.devclient.test:id/filled = 0x7f08009a
expo.modules.devclient.test:attr/actionBarTheme = 0x7f030009
expo.modules.devclient.test:attr/checkedIconVisible = 0x7f030088
expo.modules.devclient.test:attr/errorContentDescription = 0x7f03010b
expo.modules.devclient.test:dimen/design_snackbar_padding_vertical = 0x7f060087
expo.modules.devclient.test:layout/notification_action_tombstone = 0x7f0b0056
expo.modules.devclient.test:attr/dividerPadding = 0x7f0300ed
expo.modules.devclient.test:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f060145
expo.modules.devclient.test:attr/displayOptions = 0x7f0300ea
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_terminalicon = 0x7f070038
expo.modules.devclient.test:attr/actionModePasteDrawable = 0x7f030017
expo.modules.devclient.test:id/mtrl_picker_header_title_and_selection = 0x7f0800dd
expo.modules.devclient.test:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_loader = 0x7f07000a
expo.modules.devclient.test:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f0f029a
expo.modules.devclient.test:attr/colorOnPrimary = 0x7f0300b4
expo.modules.devclient.test:attr/yearStyle = 0x7f0302bd
expo.modules.devclient.test:attr/dialogTheme = 0x7f0300e9
expo.modules.devclient.test:attr/actionModeFindDrawable = 0x7f030016
expo.modules.devclient.test:attr/dialogPreferredPadding = 0x7f0300e8
expo.modules.devclient.test:dimen/abc_dialog_fixed_height_major = 0x7f06001c
expo.modules.devclient.test:attr/placeholderImage = 0x7f0301d5
expo.modules.devclient.test:drawable/ic_call_answer = 0x7f0700a6
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f0f01ff
expo.modules.devclient.test:attr/materialCalendarFullscreenTheme = 0x7f0301a9
expo.modules.devclient.test:style/Widget.AppCompat.ImageButton = 0x7f0f022f
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f015c
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f0019
expo.modules.devclient.test:animator/fragment_close_enter = 0x7f020003
expo.modules.devclient.test:attr/defaultQueryHint = 0x7f0300e6
expo.modules.devclient.test:integer/react_native_dev_server_port = 0x7f090013
expo.modules.devclient.test:attr/backgroundInsetBottom = 0x7f030041
expo.modules.devclient.test:attr/counterEnabled = 0x7f0300db
expo.modules.devclient.test:attr/dayStyle = 0x7f0300e4
expo.modules.devclient.test:dimen/mtrl_slider_track_side_padding = 0x7f06012e
expo.modules.devclient.test:attr/closeIconSize = 0x7f0300a1
expo.modules.devclient.test:attr/editTextStyle = 0x7f0300fd
expo.modules.devclient.test:id/accessibility_custom_action_5 = 0x7f080029
expo.modules.devclient.test:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07008f
expo.modules.devclient.test:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f004d
expo.modules.devclient.test:string/catalyst_debug_error = 0x7f0e0029
expo.modules.devclient.test:color/radiobutton_themeable_attribute_color = 0x7f0500c7
expo.modules.devclient.test:attr/elevation = 0x7f0300fe
expo.modules.devclient.test:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07009b
expo.modules.devclient.test:style/Base.DialogWindowTitle.AppCompat = 0x7f0f0010
expo.modules.devclient.test:attr/counterTextAppearance = 0x7f0300df
expo.modules.devclient.test:attr/behavior_overlapTop = 0x7f030055
expo.modules.devclient.test:attr/itemShapeFillColor = 0x7f03016d
expo.modules.devclient.test:attr/alertDialogCenterButtons = 0x7f030029
expo.modules.devclient.test:dimen/mtrl_high_ripple_default_alpha = 0x7f060114
expo.modules.devclient.test:style/Widget.Design.AppBarLayout = 0x7f0f0267
expo.modules.devclient.test:attr/autofillInlineSuggestionEndIconStyle = 0x7f03003a
expo.modules.devclient.test:attr/buttonGravity = 0x7f030070
expo.modules.devclient.test:style/TextAppearance.Compat.Notification = 0x7f0f0166
expo.modules.devclient.test:attr/dropDownListViewStyle = 0x7f0300f9
expo.modules.devclient.test:style/Platform.V21.AppCompat.Light = 0x7f0f00ff
expo.modules.devclient.test:attr/cornerSizeTopRight = 0x7f0300da
expo.modules.devclient.test:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
expo.modules.devclient.test:dimen/mtrl_calendar_navigation_height = 0x7f0600e4
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_branchicon = 0x7f070015
expo.modules.devclient.test:attr/dividerVertical = 0x7f0300ee
expo.modules.devclient.test:color/background_floating_material_dark = 0x7f05001d
expo.modules.devclient.test:attr/dividerHorizontal = 0x7f0300ec
expo.modules.devclient.test:id/right_icon = 0x7f0800fd
expo.modules.devclient.test:id/month_grid = 0x7f0800c8
expo.modules.devclient.test:id/action_context_bar = 0x7f08003d
expo.modules.devclient.test:attr/cornerFamilyBottomRight = 0x7f0300d2
expo.modules.devclient.test:drawable/autofill_inline_suggestion_chip_background = 0x7f070093
expo.modules.devclient.test:dimen/mtrl_textinput_box_stroke_width_default = 0x7f060139
expo.modules.devclient.test:attr/textAppearanceListItemSecondary = 0x7f03026f
expo.modules.devclient.test:attr/coordinatorLayoutStyle = 0x7f0300cf
expo.modules.devclient.test:attr/fabSize = 0x7f030123
expo.modules.devclient.test:attr/badgeGravity = 0x7f03004a
expo.modules.devclient.test:style/TextAppearance.MaterialComponents.Headline6 = 0x7f0f0181
expo.modules.devclient.test:attr/behavior_skipCollapsed = 0x7f030058
expo.modules.devclient.test:attr/contentPaddingTop = 0x7f0300cc
expo.modules.devclient.test:attr/cornerFamilyTopRight = 0x7f0300d4
expo.modules.devclient.test:drawable/ic_call_decline_low = 0x7f0700ab
expo.modules.devclient.test:attr/tabInlineLabel = 0x7f030252
expo.modules.devclient.test:id/activity_chooser_view_content = 0x7f080047
expo.modules.devclient.test:attr/shapeAppearance = 0x7f03020e
expo.modules.devclient.test:drawable/test_custom_background = 0x7f0700d3
expo.modules.devclient.test:dimen/design_snackbar_action_inline_max_width = 0x7f06007f
expo.modules.devclient.test:color/design_dark_default_color_primary_dark = 0x7f050040
expo.modules.devclient.test:dimen/mtrl_slider_thumb_elevation = 0x7f06012b
expo.modules.devclient.test:attr/boxStrokeWidth = 0x7f030068
expo.modules.devclient.test:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f0217
expo.modules.devclient.test:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
expo.modules.devclient.test:attr/iconTint = 0x7f030157
expo.modules.devclient.test:attr/cornerSizeBottomRight = 0x7f0300d8
expo.modules.devclient.test:attr/themeLineHeight = 0x7f030280
expo.modules.devclient.test:attr/textStartPadding = 0x7f03027e
expo.modules.devclient.test:dimen/design_bottom_navigation_active_item_min_width = 0x7f060063
expo.modules.devclient.test:attr/cornerFamily = 0x7f0300d0
expo.modules.devclient.test:attr/retryImageScaleType = 0x7f0301f3
expo.modules.devclient.test:attr/contentInsetRight = 0x7f0300c5
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f0f01d2
expo.modules.devclient.test:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f06013a
expo.modules.devclient.test:attr/foregroundInsidePadding = 0x7f03013a
expo.modules.devclient.test:dimen/mtrl_btn_icon_btn_padding_left = 0x7f0600bb
expo.modules.devclient.test:attr/yearTodayStyle = 0x7f0302be
expo.modules.devclient.test:attr/contentInsetEnd = 0x7f0300c2
expo.modules.devclient.test:styleable/RangeSlider = 0x7f100049
expo.modules.devclient.test:attr/state_lifted = 0x7f030232
expo.modules.devclient.test:dimen/abc_text_size_display_4_material = 0x7f060046
expo.modules.devclient.test:attr/placeholderImageScaleType = 0x7f0301d6
expo.modules.devclient.test:style/Platform.AppCompat.Light = 0x7f0f00f6
expo.modules.devclient.test:attr/buttonPanelSideLayout = 0x7f030072
expo.modules.devclient.test:styleable/RecycleListView = 0x7f10004a
expo.modules.devclient.test:string/catalyst_debug_open = 0x7f0e002a
expo.modules.devclient.test:attr/bottomSheetStyle = 0x7f03005e
expo.modules.devclient.test:attr/subtitleTextAppearance = 0x7f03023b
expo.modules.devclient.test:animator/fragment_open_enter = 0x7f020007
expo.modules.devclient.test:attr/materialCalendarHeaderConfirmButton = 0x7f0301aa
expo.modules.devclient.test:attr/contentDescription = 0x7f0300c1
expo.modules.devclient.test:style/CalendarDatePickerStyle = 0x7f0f00e3
expo.modules.devclient.test:color/design_fab_shadow_start_color = 0x7f050055
expo.modules.devclient.test:attr/drawerArrowStyle = 0x7f0300f8
expo.modules.devclient.test:color/material_deep_teal_200 = 0x7f050071
expo.modules.devclient.test:drawable/abc_switch_thumb_material = 0x7f070085
expo.modules.devclient.test:style/ShapeAppearanceOverlay.BottomLeftDifferentCornerSize = 0x7f0f011b
expo.modules.devclient.test:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0600e6
expo.modules.devclient.test:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f0097
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_shakedeviceicon = 0x7f070034
expo.modules.devclient.test:attr/colorSecondary = 0x7f0300bc
expo.modules.devclient.test:dimen/notification_content_margin_start = 0x7f060149
expo.modules.devclient.test:dimen/mtrl_badge_radius = 0x7f0600aa
expo.modules.devclient.test:color/mtrl_indicator_text_color = 0x7f0500a9
expo.modules.devclient.test:layout/redbox_item_title = 0x7f0b005d
expo.modules.devclient.test:attr/tintMode = 0x7f03028f
expo.modules.devclient.test:attr/listItemLayout = 0x7f030193
expo.modules.devclient.test:drawable/__node_modules_reactnative_libraries_logbox_ui_logboximages_close = 0x7f070009
expo.modules.devclient.test:attr/buttonStyle = 0x7f030073
expo.modules.devclient.test:dimen/mtrl_navigation_item_icon_padding = 0x7f060120
expo.modules.devclient.test:attr/colorPrimary = 0x7f0300b8
expo.modules.devclient.test:attr/progressBarStyle = 0x7f0301e8
expo.modules.devclient.test:attr/colorOnError = 0x7f0300b3
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f0f01ba
expo.modules.devclient.test:string/mtrl_picker_day_of_week_column_header = 0x7f0e006a
expo.modules.devclient.test:attr/iconStartPadding = 0x7f030156
expo.modules.devclient.test:style/TextAppearance.Compat.Notification.Info = 0x7f0f0167
expo.modules.devclient.test:id/collapseActionView = 0x7f080071
expo.modules.devclient.test:attr/fabCradleMargin = 0x7f03011f
expo.modules.devclient.test:string/catalyst_open_debugger_error = 0x7f0e0036
expo.modules.devclient.test:attr/materialAlertDialogTitleIconStyle = 0x7f0301a2
expo.modules.devclient.test:style/Widget.AppCompat.ProgressBar = 0x7f0f024e
expo.modules.devclient.test:id/async = 0x7f08004d
expo.modules.devclient.test:dimen/notification_large_icon_height = 0x7f06014a
expo.modules.devclient.test:color/mtrl_filled_background_color = 0x7f0500a6
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f0f00da
expo.modules.devclient.test:style/Animation.AppCompat.Dialog = 0x7f0f0003
expo.modules.devclient.test:attr/arrowShaftLength = 0x7f030032
expo.modules.devclient.test:attr/viewInflaterClass = 0x7f0302b0
expo.modules.devclient.test:id/SHIFT = 0x7f080006
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f0f02c7
expo.modules.devclient.test:id/scrollView = 0x7f080112
expo.modules.devclient.test:dimen/abc_search_view_preferred_height = 0x7f060036
expo.modules.devclient.test:dimen/abc_control_corner_material = 0x7f060018
expo.modules.devclient.test:attr/colorControlHighlight = 0x7f0300af
expo.modules.devclient.test:styleable/ViewStubCompat = 0x7f100066
expo.modules.devclient.test:attr/expandedTitleMarginBottom = 0x7f030116
expo.modules.devclient.test:style/Animation.Design.BottomSheetDialog = 0x7f0f0008
expo.modules.devclient.test:attr/color = 0x7f0300aa
expo.modules.devclient.test:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f0f0285
expo.modules.devclient.test:id/action_menu_presenter = 0x7f080041
expo.modules.devclient.test:anim/catalyst_slide_up = 0x7f01001d
expo.modules.devclient.test:attr/materialCalendarTheme = 0x7f0301b1
expo.modules.devclient.test:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f0188
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_extensionsfilledinactiveicon = 0x7f07001f
expo.modules.devclient.test:dimen/mtrl_navigation_item_horizontal_padding = 0x7f06011f
expo.modules.devclient.test:color/material_grey_600 = 0x7f050076
expo.modules.devclient.test:id/submenuarrow = 0x7f080133
expo.modules.devclient.test:attr/materialAlertDialogBodyTextStyle = 0x7f0301a0
expo.modules.devclient.test:string/summary_description = 0x7f0e0095
expo.modules.devclient.test:attr/closeIconEnabled = 0x7f03009f
expo.modules.devclient.test:attr/ratingBarStyleSmall = 0x7f0301f0
expo.modules.devclient.test:attr/closeIconStartPadding = 0x7f0300a2
expo.modules.devclient.test:animator/mtrl_btn_unelevated_state_list_anim = 0x7f02000a
expo.modules.devclient.test:attr/colorControlActivated = 0x7f0300ae
expo.modules.devclient.test:xml/rn_dev_preferences = 0x7f110001
expo.modules.devclient.test:layout/error_fragment = 0x7f0b0035
expo.modules.devclient.test:attr/fastScrollHorizontalTrackDrawable = 0x7f030129
expo.modules.devclient.test:id/save_overlay_view = 0x7f08010c
expo.modules.devclient.test:attr/colorBackgroundFloating = 0x7f0300ac
expo.modules.devclient.test:styleable/StateListDrawableItem = 0x7f100057
expo.modules.devclient.test:attr/collapseIcon = 0x7f0300a7
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f0f00d7
expo.modules.devclient.test:string/spinbutton_description = 0x7f0e008b
expo.modules.devclient.test:attr/shrinkMotionSpec = 0x7f030219
expo.modules.devclient.test:id/month_navigation_previous = 0x7f0800cc
expo.modules.devclient.test:attr/selectableItemBackgroundBorderless = 0x7f03020c
expo.modules.devclient.test:attr/fastScrollVerticalTrackDrawable = 0x7f03012b
expo.modules.devclient.test:attr/checkMarkCompat = 0x7f03007f
expo.modules.devclient.test:attr/cornerSizeBottomLeft = 0x7f0300d7
expo.modules.devclient.test:style/TextAppearance.AppCompat.Medium = 0x7f0f0147
expo.modules.devclient.test:string/mtrl_exceed_max_badge_number_suffix = 0x7f0e0061
expo.modules.devclient.test:attr/chipIcon = 0x7f03008e
expo.modules.devclient.test:drawable/abc_star_half_black_48dp = 0x7f070084
expo.modules.devclient.test:attr/srcCompat = 0x7f030226
expo.modules.devclient.test:attr/actionLayout = 0x7f03000d
expo.modules.devclient.test:attr/closeIconEndPadding = 0x7f0300a0
expo.modules.devclient.test:attr/fabCustomSize = 0x7f030122
expo.modules.devclient.test:dimen/mtrl_tooltip_padding = 0x7f060144
expo.modules.devclient.test:attr/switchPadding = 0x7f030244
expo.modules.devclient.test:attr/hintEnabled = 0x7f03014a
expo.modules.devclient.test:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0600ea
expo.modules.devclient.test:attr/behavior_autoShrink = 0x7f03004f
expo.modules.devclient.test:attr/tabBackground = 0x7f030247
expo.modules.devclient.test:id/custom = 0x7f080079
expo.modules.devclient.test:attr/spinBars = 0x7f030222
expo.modules.devclient.test:anim/design_snackbar_out = 0x7f010021
expo.modules.devclient.test:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f0f02b5
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f0f020d
expo.modules.devclient.test:attr/chipStrokeColor = 0x7f03009a
expo.modules.devclient.test:id/peekHeight = 0x7f0800f3
expo.modules.devclient.test:attr/chipStartPadding = 0x7f030099
expo.modules.devclient.test:string/catalyst_perf_monitor_stop = 0x7f0e0038
expo.modules.devclient.test:attr/chipStandaloneStyle = 0x7f030098
expo.modules.devclient.test:attr/buttonTintMode = 0x7f030076
expo.modules.devclient.test:color/androidx_core_ripple_material_light = 0x7f05001b
expo.modules.devclient.test:dimen/mtrl_calendar_navigation_top_padding = 0x7f0600e5
expo.modules.devclient.test:dimen/mtrl_alert_dialog_background_inset_top = 0x7f0600a6
expo.modules.devclient.test:id/view_tag_instance_handle = 0x7f08016b
expo.modules.devclient.test:attr/actionMenuTextAppearance = 0x7f03000e
expo.modules.devclient.test:styleable/TextInputEditText = 0x7f10005e
expo.modules.devclient.test:string/mtrl_picker_text_input_date_hint = 0x7f0e0077
expo.modules.devclient.test:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
expo.modules.devclient.test:color/error_color_material_dark = 0x7f050068
expo.modules.devclient.test:dimen/test_mtrl_calendar_day_cornerSize = 0x7f060155
expo.modules.devclient.test:attr/actionMenuTextColor = 0x7f03000f
expo.modules.devclient.test:attr/chipSpacing = 0x7f030095
expo.modules.devclient.test:attr/itemTextAppearanceActive = 0x7f030176
expo.modules.devclient.test:attr/errorIconDrawable = 0x7f03010d
expo.modules.devclient.test:attr/chipMinTouchTargetSize = 0x7f030094
expo.modules.devclient.test:attr/tooltipText = 0x7f0302a1
expo.modules.devclient.test:attr/chipIconTint = 0x7f030091
expo.modules.devclient.test:attr/imageButtonStyle = 0x7f03015a
expo.modules.devclient.test:id/listMode = 0x7f0800bf
expo.modules.devclient.test:attr/cardForegroundColor = 0x7f03007a
expo.modules.devclient.test:styleable/BottomAppBar = 0x7f100015
expo.modules.devclient.test:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f0156
expo.modules.devclient.test:attr/cornerFamilyTopLeft = 0x7f0300d3
expo.modules.devclient.test:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f00be
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_checkicon = 0x7f070018
expo.modules.devclient.test:attr/chipIconSize = 0x7f030090
expo.modules.devclient.test:dimen/mtrl_bottomappbar_height = 0x7f0600b3
expo.modules.devclient.test:style/Widget.Design.TabLayout = 0x7f0f026f
expo.modules.devclient.test:id/FUNCTION = 0x7f080004
expo.modules.devclient.test:attr/chipIconEnabled = 0x7f03008f
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f002f
expo.modules.devclient.test:attr/materialCalendarHeaderLayout = 0x7f0301ac
expo.modules.devclient.test:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f0600ab
expo.modules.devclient.test:attr/drawableTopCompat = 0x7f0300f7
expo.modules.devclient.test:attr/cornerRadius = 0x7f0300d5
expo.modules.devclient.test:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f00fd
expo.modules.devclient.test:attr/fabCradleRoundedCornerRadius = 0x7f030120
expo.modules.devclient.test:attr/tabPaddingStart = 0x7f030259
expo.modules.devclient.test:attr/chipGroupStyle = 0x7f03008d
expo.modules.devclient.test:dimen/abc_text_size_body_1_material = 0x7f06003f
expo.modules.devclient.test:attr/boxStrokeErrorColor = 0x7f030067
expo.modules.devclient.test:color/material_on_surface_emphasis_high_type = 0x7f050081
expo.modules.devclient.test:attr/chipEndPadding = 0x7f03008c
expo.modules.devclient.test:style/Widget.MaterialComponents.Tooltip = 0x7f0f02d1
expo.modules.devclient.test:attr/passwordToggleTintMode = 0x7f0301d4
expo.modules.devclient.test:style/Widget.Design.FloatingActionButton = 0x7f0f026b
expo.modules.devclient.test:attr/chipCornerRadius = 0x7f03008b
expo.modules.devclient.test:dimen/mtrl_extended_fab_bottom_padding = 0x7f0600ff
expo.modules.devclient.test:attr/rangeFillColor = 0x7f0301ed
expo.modules.devclient.test:style/Widget.AppCompat.ActionButton = 0x7f0f021b
expo.modules.devclient.test:dimen/mtrl_calendar_header_selection_line_height = 0x7f0600db
expo.modules.devclient.test:attr/elevationOverlayColor = 0x7f0300ff
expo.modules.devclient.test:string/menuitem_description = 0x7f0e005d
expo.modules.devclient.test:dimen/mtrl_calendar_year_corner = 0x7f0600ee
expo.modules.devclient.test:attr/listPreferredItemPaddingEnd = 0x7f03019a
expo.modules.devclient.test:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070063
expo.modules.devclient.test:color/material_on_surface_emphasis_medium = 0x7f050082
expo.modules.devclient.test:attr/chipBackgroundColor = 0x7f03008a
expo.modules.devclient.test:dimen/design_bottom_navigation_shadow_height = 0x7f06006b
expo.modules.devclient.test:styleable/CardView = 0x7f10001a
expo.modules.devclient.test:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070099
expo.modules.devclient.test:attr/cardUseCompatPadding = 0x7f03007d
expo.modules.devclient.test:attr/checkedTextViewStyle = 0x7f030089
expo.modules.devclient.test:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f0f01d6
expo.modules.devclient.test:attr/tabIndicatorFullWidth = 0x7f03024f
expo.modules.devclient.test:attr/actionModeBackground = 0x7f030010
expo.modules.devclient.test:attr/autoSizeTextType = 0x7f030038
expo.modules.devclient.test:attr/actionModeWebSearchDrawable = 0x7f03001e
expo.modules.devclient.test:id/textinput_suffix_text = 0x7f080154
expo.modules.devclient.test:anim/fragment_fast_out_extra_slow_in = 0x7f010022
expo.modules.devclient.test:attr/height = 0x7f030141
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_extensionsfilledactiveicon = 0x7f07001d
expo.modules.devclient.test:attr/layout_keyline = 0x7f030188
expo.modules.devclient.test:attr/checkMarkTintMode = 0x7f030081
expo.modules.devclient.test:animator/design_appbar_state_list_animator = 0x7f020000
expo.modules.devclient.test:attr/layout_behavior = 0x7f030183
expo.modules.devclient.test:attr/labelVisibilityMode = 0x7f03017d
expo.modules.devclient.test:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f0f01c0
expo.modules.devclient.test:attr/bottomAppBarStyle = 0x7f03005b
expo.modules.devclient.test:attr/colorOnBackground = 0x7f0300b2
expo.modules.devclient.test:dimen/abc_text_size_menu_material = 0x7f06004b
expo.modules.devclient.test:string/status_bar_notification_info_overflow = 0x7f0e0094
expo.modules.devclient.test:attr/measureWithLargestChild = 0x7f0301b9
expo.modules.devclient.test:color/material_on_primary_emphasis_high_type = 0x7f05007e
expo.modules.devclient.test:attr/dayTodayStyle = 0x7f0300e5
expo.modules.devclient.test:string/state_collapsed_description = 0x7f0e008e
expo.modules.devclient.test:attr/closeItemLayout = 0x7f0300a5
expo.modules.devclient.test:color/abc_secondary_text_material_light = 0x7f050012
expo.modules.devclient.test:drawable/tooltip_frame_dark = 0x7f0700d5
expo.modules.devclient.test:attr/chipIconVisible = 0x7f030092
expo.modules.devclient.test:attr/fontProviderQuery = 0x7f030135
expo.modules.devclient.test:anim/catalyst_fade_in = 0x7f010018
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f0f01fb
expo.modules.devclient.test:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
expo.modules.devclient.test:id/accessibility_custom_action_8 = 0x7f08002c
expo.modules.devclient.test:attr/behavior_draggable = 0x7f030050
expo.modules.devclient.test:attr/itemSpacing = 0x7f030172
expo.modules.devclient.test:id/save_non_transition_alpha = 0x7f08010b
expo.modules.devclient.test:attr/cardElevation = 0x7f030079
expo.modules.devclient.test:id/CTRL = 0x7f080003
expo.modules.devclient.test:color/tooltip_background_dark = 0x7f0500d6
expo.modules.devclient.test:attr/contentPadding = 0x7f0300c8
expo.modules.devclient.test:attr/listPreferredItemHeightSmall = 0x7f030199
expo.modules.devclient.test:attr/alertDialogButtonGroupStyle = 0x7f030028
expo.modules.devclient.test:style/Base.Widget.MaterialComponents.Chip = 0x7f0f00d9
expo.modules.devclient.test:attr/boxCornerRadiusBottomStart = 0x7f030063
expo.modules.devclient.test:attr/expandedTitleMarginStart = 0x7f030118
expo.modules.devclient.test:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f0229
expo.modules.devclient.test:id/edit_query = 0x7f080087
expo.modules.devclient.test:attr/progressBarAutoRotateInterval = 0x7f0301e4
expo.modules.devclient.test:attr/failureImageScaleType = 0x7f030126
expo.modules.devclient.test:id/title = 0x7f080156
expo.modules.devclient.test:dimen/abc_text_size_headline_material = 0x7f060047
expo.modules.devclient.test:attr/emojiCompatEnabled = 0x7f030101
expo.modules.devclient.test:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f0f01e0
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0022
expo.modules.devclient.test:anim/design_snackbar_in = 0x7f010020
expo.modules.devclient.test:attr/paddingTopNoTitle = 0x7f0301cc
expo.modules.devclient.test:attr/actionBarTabBarStyle = 0x7f030006
expo.modules.devclient.test:attr/checkedIconTint = 0x7f030087
expo.modules.devclient.test:color/background_material_dark = 0x7f05001f
expo.modules.devclient.test:dimen/design_bottom_navigation_active_text_size = 0x7f060064
expo.modules.devclient.test:attr/bottomNavigationStyle = 0x7f03005c
expo.modules.devclient.test:attr/subMenuArrow = 0x7f030238
expo.modules.devclient.test:xml/image_share_filepaths = 0x7f110000
expo.modules.devclient.test:attr/buttonCompat = 0x7f03006f
expo.modules.devclient.test:attr/alphabeticModifiers = 0x7f03002e
expo.modules.devclient.test:bool/abc_action_bar_embed_tabs = 0x7f040000
expo.modules.devclient.test:attr/cardCornerRadius = 0x7f030078
expo.modules.devclient.test:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f00a0
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_checkiconlight = 0x7f070019
expo.modules.devclient.test:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f00d3
expo.modules.devclient.test:id/accessibility_custom_action_1 = 0x7f08000f
expo.modules.devclient.test:styleable/MaterialButtonToggleGroup = 0x7f10003a
expo.modules.devclient.test:attr/suffixTextColor = 0x7f030240
expo.modules.devclient.test:dimen/design_bottom_navigation_icon_size = 0x7f060067
expo.modules.devclient.test:attr/listPreferredItemPaddingRight = 0x7f03019c
expo.modules.devclient.test:attr/fontProviderSystemFontFamily = 0x7f030136
expo.modules.devclient.test:layout/abc_tooltip = 0x7f0b001b
expo.modules.devclient.test:animator/fragment_fade_enter = 0x7f020005
expo.modules.devclient.test:string/menu_description = 0x7f0e005b
expo.modules.devclient.test:attr/state_liftable = 0x7f030231
expo.modules.devclient.test:drawable/notification_template_icon_bg = 0x7f0700cb
expo.modules.devclient.test:attr/cardPreventCornerOverlap = 0x7f03007c
expo.modules.devclient.test:attr/hideMotionSpec = 0x7f030146
expo.modules.devclient.test:attr/expandedTitleTextAppearance = 0x7f03011a
expo.modules.devclient.test:anim/abc_slide_in_top = 0x7f010007
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_infoicon = 0x7f070027
expo.modules.devclient.test:attr/boxBackgroundColor = 0x7f03005f
expo.modules.devclient.test:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f0f02c5
expo.modules.devclient.test:string/copy_toast_msg = 0x7f0e0046
expo.modules.devclient.test:attr/borderWidth = 0x7f030059
expo.modules.devclient.test:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0232
expo.modules.devclient.test:layout/autofill_inline_suggestion = 0x7f0b001d
expo.modules.devclient.test:drawable/__node_modules_reactnavigation_elements_lib_module_assets_clearicon = 0x7f07000d
expo.modules.devclient.test:attr/fontFamily = 0x7f03012f
expo.modules.devclient.test:style/Platform.MaterialComponents.Light = 0x7f0f00f9
expo.modules.devclient.test:attr/behavior_halfExpandedRatio = 0x7f030053
expo.modules.devclient.test:color/material_grey_900 = 0x7f050079
expo.modules.devclient.test:style/DialogAnimationFade = 0x7f0f00e7
expo.modules.devclient.test:attr/submitBackground = 0x7f030239
expo.modules.devclient.test:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f060133
expo.modules.devclient.test:attr/backgroundInsetStart = 0x7f030043
expo.modules.devclient.test:layout/mtrl_alert_dialog = 0x7f0b0039
expo.modules.devclient.test:attr/titleTextStyle = 0x7f03029a
expo.modules.devclient.test:anim/abc_fade_in = 0x7f010000
expo.modules.devclient.test:attr/actionModeSplitBackground = 0x7f03001b
expo.modules.devclient.test:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02000e
expo.modules.devclient.test:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
expo.modules.devclient.test:attr/boxBackgroundMode = 0x7f030060
expo.modules.devclient.test:attr/arrowHeadLength = 0x7f030031
expo.modules.devclient.test:style/Theme.MaterialComponents = 0x7f0f01b0
expo.modules.devclient.test:id/filter = 0x7f08009b
expo.modules.devclient.test:attr/overlapAnchor = 0x7f0301c4
expo.modules.devclient.test:animator/mtrl_extended_fab_state_list_animator = 0x7f020010
expo.modules.devclient.test:attr/textAppearanceSubtitle2 = 0x7f030277
expo.modules.devclient.test:attr/badgeStyle = 0x7f03004b
expo.modules.devclient.test:attr/backgroundTint = 0x7f030048
expo.modules.devclient.test:attr/itemShapeInsetEnd = 0x7f03016f
expo.modules.devclient.test:drawable/abc_text_select_handle_right_mtrl = 0x7f07008c
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f0f020e
expo.modules.devclient.test:attr/backgroundInsetTop = 0x7f030044
expo.modules.devclient.test:dimen/abc_text_size_subhead_material = 0x7f06004d
expo.modules.devclient.test:attr/boxStrokeWidthFocused = 0x7f030069
expo.modules.devclient.test:attr/expandedTitleMarginTop = 0x7f030119
expo.modules.devclient.test:drawable/__node_modules_reactnavigation_elements_lib_module_assets_backiconmask = 0x7f07000c
expo.modules.devclient.test:id/left = 0x7f0800bc
expo.modules.devclient.test:attr/startIconTintMode = 0x7f03022c
expo.modules.devclient.test:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f0240
expo.modules.devclient.test:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070044
expo.modules.devclient.test:attr/actionBarTabTextStyle = 0x7f030008
expo.modules.devclient.test:id/expand_activities_button = 0x7f080094
expo.modules.devclient.test:attr/searchIcon = 0x7f030208
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Title = 0x7f0f002e
expo.modules.devclient.test:attr/actionOverflowButtonStyle = 0x7f03001f
expo.modules.devclient.test:style/Platform.V25.AppCompat.Light = 0x7f0f0101
expo.modules.devclient.test:attr/colorError = 0x7f0300b1
expo.modules.devclient.test:attr/layout_collapseParallaxMultiplier = 0x7f030185
expo.modules.devclient.test:attr/actionModePopupWindowStyle = 0x7f030018
expo.modules.devclient.test:drawable/notification_icon_background = 0x7f0700c9
expo.modules.devclient.test:attr/expandedTitleMargin = 0x7f030115
expo.modules.devclient.test:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
expo.modules.devclient.test:id/slide = 0x7f080125
expo.modules.devclient.test:attr/autoSizeStepGranularity = 0x7f030037
expo.modules.devclient.test:string/fab_transformation_scrim_behavior = 0x7f0e004d
expo.modules.devclient.test:attr/behavior_expandedOffset = 0x7f030051
expo.modules.devclient.test:attr/statusBarBackground = 0x7f030233
expo.modules.devclient.test:attr/state_dragged = 0x7f030230
expo.modules.devclient.test:attr/hintTextColor = 0x7f03014c
expo.modules.devclient.test:attr/ensureMinTouchTargetSize = 0x7f03010a
expo.modules.devclient.test:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f018f
expo.modules.devclient.test:dimen/abc_panel_menu_list_width = 0x7f060034
expo.modules.devclient.test:attr/materialCalendarHeaderTitle = 0x7f0301ae
expo.modules.devclient.test:attr/alertDialogStyle = 0x7f03002a
expo.modules.devclient.test:attr/actualImageScaleType = 0x7f030026
expo.modules.devclient.test:attr/tabUnboundedRipple = 0x7f030260
expo.modules.devclient.test:id/mtrl_picker_text_input_range_start = 0x7f0800e1
expo.modules.devclient.test:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0600e3
expo.modules.devclient.test:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f00c0
expo.modules.devclient.test:attr/snackbarStyle = 0x7f03021f
expo.modules.devclient.test:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f0600b2
expo.modules.devclient.test:attr/divider = 0x7f0300eb
expo.modules.devclient.test:attr/actionModeStyle = 0x7f03001c
expo.modules.devclient.test:string/abc_toolbar_collapse_description = 0x7f0e001a
expo.modules.devclient.test:dimen/mtrl_low_ripple_default_alpha = 0x7f060119
expo.modules.devclient.test:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f0f0121
expo.modules.devclient.test:attr/minTouchTargetSize = 0x7f0301bb
expo.modules.devclient.test:attr/fontStyle = 0x7f030137
expo.modules.devclient.test:id/accessibility_custom_action_23 = 0x7f08001e
expo.modules.devclient.test:attr/materialButtonStyle = 0x7f0301a6
expo.modules.devclient.test:id/ALT = 0x7f080000
expo.modules.devclient.test:style/Theme.FullScreenDialogAnimatedSlide = 0x7f0f01af
expo.modules.devclient.test:attr/liftOnScroll = 0x7f03018b
expo.modules.devclient.test:dimen/tooltip_y_offset_touch = 0x7f06015d
expo.modules.devclient.test:dimen/notification_large_icon_width = 0x7f06014b
expo.modules.devclient.test:anim/abc_popup_enter = 0x7f010003
expo.modules.devclient.test:color/material_slider_active_tick_marks_color = 0x7f050084
expo.modules.devclient.test:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f0f01ca
expo.modules.devclient.test:attr/textAppearanceListItem = 0x7f03026e
expo.modules.devclient.test:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07005a
expo.modules.devclient.test:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f0f0072
expo.modules.devclient.test:drawable/mtrl_ic_error = 0x7f0700bd
expo.modules.devclient.test:attr/actionOverflowMenuStyle = 0x7f030020
expo.modules.devclient.test:style/Theme.AppCompat.DayNight = 0x7f0f018c
expo.modules.devclient.test:id/BOTTOM_END = 0x7f080001
expo.modules.devclient.test:attr/expandedTitleGravity = 0x7f030114
expo.modules.devclient.test:color/dev_launcher_colorAccentDark = 0x7f05005d
expo.modules.devclient.test:attr/layout_anchor = 0x7f030181
expo.modules.devclient.test:styleable/SimpleDraweeView = 0x7f100051
expo.modules.devclient.test:dimen/abc_action_bar_default_height_material = 0x7f060002
expo.modules.devclient.test:attr/actionBarPopupTheme = 0x7f030002
expo.modules.devclient.test:attr/gestureInsetBottomIgnored = 0x7f03013c
expo.modules.devclient.test:attr/autofillInlineSuggestionSubtitle = 0x7f03003c
expo.modules.devclient.test:anim/abc_slide_out_bottom = 0x7f010008
expo.modules.devclient.test:style/AndroidThemeColorAccentYellow = 0x7f0f0002
expo.modules.devclient.test:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0500ad
expo.modules.devclient.test:attr/bottomSheetDialogTheme = 0x7f03005d
expo.modules.devclient.test:layout/bottom_sheet = 0x7f0b001e
expo.modules.devclient.test:id/action_bar_container = 0x7f080037
expo.modules.devclient.test:color/bright_foreground_inverse_material_light = 0x7f050024
expo.modules.devclient.test:attr/tabPaddingTop = 0x7f03025a
expo.modules.devclient.test:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f0f0211
expo.modules.devclient.test:drawable/design_ic_visibility = 0x7f0700a0
expo.modules.devclient.test:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f014a
expo.modules.devclient.test:color/abc_primary_text_material_dark = 0x7f05000b
expo.modules.devclient.test:anim/catalyst_slide_down = 0x7f01001c
expo.modules.devclient.test:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f0600b0
expo.modules.devclient.test:dimen/mtrl_low_ripple_pressed_alpha = 0x7f06011c
expo.modules.devclient.test:animator/mtrl_fab_show_motion_spec = 0x7f020012
expo.modules.devclient.test:attr/rippleColor = 0x7f0301f5
expo.modules.devclient.test:attr/chipSpacingHorizontal = 0x7f030096
expo.modules.devclient.test:styleable/ExtendedFloatingActionButton = 0x7f100025
expo.modules.devclient.test:animator/mtrl_extended_fab_show_motion_spec = 0x7f02000f
expo.modules.devclient.test:attr/fastScrollVerticalThumbDrawable = 0x7f03012a
expo.modules.devclient.test:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f00bf
expo.modules.devclient.test:anim/abc_fade_out = 0x7f010001
expo.modules.devclient.test:attr/trackTint = 0x7f0302a7
expo.modules.devclient.test:attr/state_above_anchor = 0x7f03022d
expo.modules.devclient.test:string/abc_menu_alt_shortcut_label = 0x7f0e0008
expo.modules.devclient.test:attr/boxCornerRadiusTopEnd = 0x7f030064
expo.modules.devclient.test:animator/mtrl_btn_state_list_anim = 0x7f020009
expo.modules.devclient.test:layout/mtrl_calendar_month_labeled = 0x7f0b0044
expo.modules.devclient.test:attr/layout_anchorGravity = 0x7f030182
expo.modules.devclient.test:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
expo.modules.devclient.test:style/TextAppearance.Compat.Notification.Time = 0x7f0f0169
expo.modules.devclient.test:color/switch_thumb_normal_material_dark = 0x7f0500d2
expo.modules.devclient.test:attr/borderlessButtonStyle = 0x7f03005a
expo.modules.devclient.test:style/Widget.MaterialComponents.ShapeableImageView = 0x7f0f02b8
expo.modules.devclient.test:drawable/btn_checkbox_checked_mtrl = 0x7f070096
expo.modules.devclient.test:drawable/_expodevclientcomponents_assets_usericonlight = 0x7f07003f
expo.modules.devclient.test:attr/transitionShapeAppearance = 0x7f0302a9
expo.modules.devclient.test:attr/actionBarTabStyle = 0x7f030007
expo.modules.devclient.test:attr/roundTopEnd = 0x7f0301fb
expo.modules.devclient.test:dimen/mtrl_switch_thumb_elevation = 0x7f060135
expo.modules.devclient.test:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f0f029c
expo.modules.devclient.test:attr/listDividerAlertDialog = 0x7f030192
expo.modules.devclient.test:dimen/abc_action_button_min_height_material = 0x7f06000d
expo.modules.devclient.test:id/role = 0x7f080108
expo.modules.devclient.test:attr/counterMaxLength = 0x7f0300dc
expo.modules.devclient.test:layout/ime_base_split_test_activity = 0x7f0b0037
expo.modules.devclient.test:attr/chipMinHeight = 0x7f030093
expo.modules.devclient.test:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f00b2
expo.modules.devclient.test:string/imagebutton_description = 0x7f0e0056
expo.modules.devclient.test:dimen/mtrl_chip_pressed_translation_z = 0x7f0600f9
expo.modules.devclient.test:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f0f0081
expo.modules.devclient.test:attr/materialCalendarHeaderToggleButton = 0x7f0301af
expo.modules.devclient.test:dimen/mtrl_card_checked_icon_margin = 0x7f0600f3
expo.modules.devclient.test:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
expo.modules.devclient.test:id/invalidate_transform = 0x7f0800b6
expo.modules.devclient.test:attr/keylines = 0x7f030179
expo.modules.devclient.test:attr/actionModeCloseButtonStyle = 0x7f030011
expo.modules.devclient.test:dimen/mtrl_btn_letter_spacing = 0x7f0600be
expo.modules.devclient.test:attr/tooltipFrameBackground = 0x7f03029f
expo.modules.devclient.test:attr/searchHintIcon = 0x7f030207
expo.modules.devclient.test:drawable/design_ic_visibility_off = 0x7f0700a1
expo.modules.devclient.test:string/abc_action_mode_done = 0x7f0e0003
expo.modules.devclient.test:id/accessibility_custom_action_22 = 0x7f08001d
expo.modules.devclient.test:id/stretch = 0x7f080132
expo.modules.devclient.test:attr/cornerFamilyBottomLeft = 0x7f0300d1
expo.modules.devclient.test:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
expo.modules.devclient.test:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020014
expo.modules.devclient.test:styleable/MaterialAutoCompleteTextView = 0x7f100038
expo.modules.devclient.test:attr/tabRippleColor = 0x7f03025b
expo.modules.devclient.test:attr/itemBackground = 0x7f030161
expo.modules.devclient.test:color/abc_tint_default = 0x7f050014
expo.modules.devclient.test:animator/mtrl_fab_hide_motion_spec = 0x7f020011
expo.modules.devclient.test:dimen/mtrl_fab_elevation = 0x7f060110
expo.modules.devclient.test:id/scrollable = 0x7f080113
expo.modules.devclient.test:attr/placeholderTextColor = 0x7f0301d9
expo.modules.devclient.test:id/accessibility_custom_action_4 = 0x7f080028
expo.modules.devclient.test:attr/colorAccent = 0x7f0300ab
expo.modules.devclient.test:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f0f02b1
expo.modules.devclient.test:attr/activityChooserViewStyle = 0x7f030024
expo.modules.devclient.test:attr/autofillInlineSuggestionChip = 0x7f030039
expo.modules.devclient.test:attr/listPreferredItemPaddingLeft = 0x7f03019b
expo.modules.devclient.test:attr/collapsedTitleGravity = 0x7f0300a8
expo.modules.devclient.test:style/Base.V7.Theme.AppCompat.Light = 0x7f0f0096
expo.modules.devclient.test:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0021
expo.modules.devclient.test:id/month_navigation_next = 0x7f0800cb
expo.modules.devclient.test:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
