1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="expo.modules.devlauncher.test" >
4
5    <uses-sdk
5-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:5:5-74
6        android:minSdkVersion="24"
6-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:5:15-41
7        android:targetSdkVersion="35" />
7-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:5:42-71
8
9    <instrumentation
9-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:11:5-15:79
10        android:name="android.test.InstrumentationTestRunner"
10-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:11:22-75
11        android:functionalTest="false"
11-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:14:22-52
12        android:handleProfiling="false"
12-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:13:22-53
13        android:label="Tests for expo.modules.devlauncher.test"
13-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:15:22-77
14        android:targetPackage="expo.modules.devlauncher.test" />
14-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:12:22-75
15
16    <queries>
16-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
17        <package android:name="host.exp.exponent" />
17-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
17-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
18    </queries>
19    <!--
20    This manifest file is used only by Gradle to configure debug-only capabilities
21    for React Native Apps.
22    -->
23    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
23-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
23-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:22-75
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="expo.modules.devlauncher.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="expo.modules.devlauncher.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30
31    <application
31-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:7:5-9:19
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
33        android:debuggable="true"
34        android:extractNativeLibs="false" >
35        <uses-library android:name="android.test.runner" />
35-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:8:9-60
35-->C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest4097858645823652541.xml:8:23-57
36
37        <activity
37-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
38            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
38-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
39            android:exported="true"
39-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
40            android:launchMode="singleTask"
40-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
41            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
41-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
42            <intent-filter>
42-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
43                <action android:name="android.intent.action.VIEW" />
43-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-69
43-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:25-66
44
45                <category android:name="android.intent.category.DEFAULT" />
45-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-76
45-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:27-73
46                <category android:name="android.intent.category.BROWSABLE" />
46-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-78
46-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:27-75
47
48                <data android:scheme="expo-dev-launcher" />
48-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-60
48-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:23-57
49            </intent-filter>
50        </activity>
51        <activity
51-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
52            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
52-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
53            android:screenOrientation="portrait"
53-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
54            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
54-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
55
56        <meta-data
56-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
57            android:name="org.unimodules.core.AppLoader#react-native-headless"
57-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
58            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
58-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
59        <meta-data
59-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
60            android:name="com.facebook.soloader.enabled"
60-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
61            android:value="true" />
61-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
62
63        <activity
63-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
64            android:name="expo.modules.devmenu.DevMenuActivity"
64-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
65            android:exported="true"
65-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
66            android:launchMode="singleTask"
66-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
67            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
67-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
68            <intent-filter>
68-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
69                <action android:name="android.intent.action.VIEW" />
69-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:17-69
69-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:25-66
70
71                <category android:name="android.intent.category.DEFAULT" />
71-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-76
71-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:27-73
72                <category android:name="android.intent.category.BROWSABLE" />
72-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-78
72-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:27-75
73
74                <data android:scheme="expo-dev-menu" />
74-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-60
74-->[:expo-dev-launcher] C:\web-app\dukancard-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:23-57
75            </intent-filter>
76        </activity>
77        <activity
77-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
78            android:name="com.facebook.react.devsupport.DevSettingsActivity"
78-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
79            android:exported="false" />
79-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
80
81        <provider
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
82            android:name="androidx.startup.InitializationProvider"
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
83            android:authorities="expo.modules.devlauncher.test.androidx-startup"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
84            android:exported="false" >
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
85            <meta-data
85-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.emoji2.text.EmojiCompatInitializer"
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
87                android:value="androidx.startup" />
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
89-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
90                android:value="androidx.startup" />
90-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
93                android:value="androidx.startup" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
94        </provider>
95
96        <receiver
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
97            android:name="androidx.profileinstaller.ProfileInstallReceiver"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
98            android:directBootAware="false"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
99            android:enabled="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
100            android:exported="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
101            android:permission="android.permission.DUMP" >
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
103                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
106                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
109                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
112                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
113            </intent-filter>
114        </receiver>
115    </application>
116
117</manifest>
