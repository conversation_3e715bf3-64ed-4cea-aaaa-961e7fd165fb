(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__c2258e89._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/utils/supabase/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "updateSession": (()=>updateSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
;
async function updateSession(request) {
    let supabaseResponse = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
        request
    });
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o"), {
        cookies: {
            getAll () {
                return request.cookies.getAll();
            },
            setAll (cookiesToSet) {
                cookiesToSet.forEach(({ name, value })=>request.cookies.set(name, value));
                supabaseResponse = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
                    request
                });
                cookiesToSet.forEach(({ name, value, options })=>supabaseResponse.cookies.set(name, value, options));
            }
        }
    });
    // Do not run code between createServerClient and
    // supabase.auth.getUser(). A simple mistake could make it very hard to debug
    // issues with users being randomly logged out.
    const { data: { user } } = await supabase.auth.getUser();
    const { pathname } = request.nextUrl;
    // Define protected path prefixes
    const protectedPrefixes = [
        "/dashboard",
        "/onboarding",
        "/choose-role"
    ];
    // Check if the current path is a protected path
    const isProtectedRoute = protectedPrefixes.some((prefix)=>pathname.startsWith(prefix));
    // Redirect to login if user is not authenticated AND accessing a protected path
    if (!user && isProtectedRoute) {
        const url = request.nextUrl.clone();
        url.pathname = "/login";
        url.searchParams.set("next", pathname); // Optionally pass the intended path
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
    }
    // If user is authenticated, perform profile checks and role-based redirects
    if (user) {
        // Check customer profile and business profile
        const [customerProfileRes, businessProfileRes] = await Promise.all([
            supabase.from("customer_profiles").select("id").eq("id", user.id).maybeSingle(),
            supabase.from("business_profiles").select("id, business_slug, trial_end_date, has_active_subscription").eq("id", user.id).maybeSingle()
        ]);
        const customerProfile = customerProfileRes.data;
        const customerError = customerProfileRes.error;
        const businessProfile = businessProfileRes.data;
        const businessError = businessProfileRes.error;
        // Check if the user just logged out
        const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';
        if (customerError || businessError) {
        // Allow request to proceed - profile fetch errors are not critical for middleware
        } else if (!customerProfile && !businessProfile) {
            // No profile found in either table - first time user
            // Allow access ONLY to the choose-role page OR the onboarding page (onboarding is technically for business only now)
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname !== "/choose-role" && pathname !== "/onboarding" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/choose-role";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        } else {
            // Profile found - determine user type
            const userType = customerProfile ? "customer" : "business";
            // If business user hasn't completed onboarding (no slug), redirect to onboarding
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (userType === "business" && !businessProfile?.business_slug && pathname !== "/onboarding" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/onboarding";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // --- CENTRALIZED SUBSCRIPTION STATUS CHECK ---
            // CRITICAL: Since we now support free plans, access is no longer restricted anywhere in the application.
            // This check is only for monitoring subscription consistency and feature-level restrictions.
            if (userType === "business" && businessProfile?.business_slug) {
            // Note: Subscription state validation could be implemented here for monitoring
            // Feature-level restrictions are handled at the component level based on plan_id
            // TODO: Consider implementing validation logic for critical inconsistencies
            }
            // --- End Centralized Subscription Status Check ---
            // --- Free Tier Feature Access Checks ---
            // Get the plan_id from the payment_subscriptions table
            const { data: subscriptionData } = await supabase.from("payment_subscriptions").select("plan_id").eq("business_profile_id", user.id).order("created_at", {
                ascending: false
            }).limit(1).maybeSingle();
            const planId = subscriptionData?.plan_id || "free";
            if (userType === "business") {
                // Free plan restrictions
                if (planId === "free") {
                    // Check for analytics access
                    if (pathname.startsWith("/dashboard/business/analytics")) {
                        // Redirect free tier users away from analytics pages
                        const url = request.nextUrl.clone();
                        url.pathname = "/dashboard/business/plan";
                        url.searchParams.set("upgrade", "analytics");
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
                    }
                }
                // Both free and basic plan restrictions
                if (planId === "free" || planId === "basic") {
                // No additional restrictions for free and basic plans currently
                }
            }
            // --- End Free Tier Feature Access Checks ---
            // Redirect away from public auth pages if logged in and profile exists,
            // UNLESS they just logged out and are heading to the login page.
            if ((pathname === "/login" || pathname === "/choose-role" || pathname === "/choose-role") && !(pathname === "/login" && justLoggedOut) // Don't redirect if just logged out and going to login
            ) {
                const redirectPath = userType === "business" ? "/dashboard/business" : "/dashboard/customer";
                const url = request.nextUrl.clone();
                url.pathname = redirectPath;
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // Redirect away from onboarding if user is a customer
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname === "/onboarding" && userType === "customer" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/customer";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // Protect dashboard routes based on user type
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname.startsWith("/dashboard/customer") && userType !== "customer" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/business"; // Redirect to their correct dashboard
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            if (pathname.startsWith("/dashboard/business") && userType !== "business" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/customer"; // Redirect to their correct dashboard
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        }
    } else {
    // User is not logged in, but accessing a public path
    }
    // --- Original commented out logic ---
    /*

  */ // --- End of original commented out logic ---
    // IMPORTANT: You *must* return the supabaseResponse object as it is.
    // If you're creating a new response object with NextResponse.next() make sure to:
    // 1. Pass the request in it, like so:
    //    const myNewResponse = NextResponse.next({ request })
    // 2. Copy over the cookies, like so:
    //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
    // 3. Change the myNewResponse object to fit your needs, but avoid changing
    //    the cookies!
    // 4. Finally:
    //    return myNewResponse
    // If this is not done, you may be causing the browser and server to go out
    // of sync and terminate the user's session prematurely!
    return supabaseResponse;
}
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$middleware$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/middleware.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$ratelimit$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@upstash/ratelimit/dist/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [middleware-edge] (ecmascript) <locals>");
;
;
;
;
// Initialize Redis client for rate limiting only
const redisUrl = process.env.UPSTASH_REDIS_REST_URL;
const redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;
if (!redisUrl || !redisToken) {
    console.error("Upstash Redis URL or Token is not defined in environment variables.");
}
const redis = redisUrl && redisToken ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Redis"]({
    url: redisUrl,
    token: redisToken
}) : null;
// Initialize Rate Limiter
const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "10");
const windowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || "10");
const ratelimit = redis ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$ratelimit$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Ratelimit"]({
    redis: redis,
    limiter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$ratelimit$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Ratelimit"].slidingWindow(maxRequests, `${windowSeconds} s`),
    analytics: true,
    prefix: "@upstash/ratelimit/dukancard"
}) : null;
async function middleware(request) {
    // --- Domain and HTTPS Redirect Logic START ---
    const url = request.nextUrl.clone();
    const hostname = url.hostname;
    const protocol = url.protocol;
    // Only apply redirects in production environment and exclude development/testing domains
    const isDevelopmentDomain = hostname.includes('localhost') || hostname.includes('ngrok.io') || hostname.includes('ngrok-free.app') || hostname.includes('127.0.0.1');
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // --- Domain and HTTPS Redirect Logic END ---
    // --- Rate Limiting Logic START ---
    // Apply rate limiting to API routes only (skip webhooks)
    if (request.nextUrl.pathname.startsWith("/api/") && !request.nextUrl.pathname.startsWith("/api/webhooks/")) {
        // Skip rate limiting if Redis is not configured
        if (!ratelimit) {
            console.warn("Rate limiting skipped: Redis not configured");
        } else {
            // Get IP address: Check 'x-forwarded-for' header first, then fallback.
            const forwardedFor = request.headers.get('x-forwarded-for');
            // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.
            const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : "127.0.0.1";
            try {
                // Use Upstash rate limiting
                const { success, limit, remaining, reset } = await ratelimit.limit(ip);
                if (!success) {
                    // Rate limit exceeded, return 429
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]("Too Many Requests", {
                        status: 429,
                        headers: {
                            "X-RateLimit-Limit": limit.toString(),
                            "X-RateLimit-Remaining": remaining.toString(),
                            "X-RateLimit-Reset": new Date(reset * 1000).toISOString()
                        }
                    });
                }
            } catch (error) {
                console.error("Rate limiting error:", error);
            // If rate limiting fails, allow the request to proceed
            }
        }
    }
    // --- Rate Limiting Logic END ---
    // Proceed with Supabase session update
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$middleware$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["updateSession"])(request);
}
const config = {
    matcher: [
        /*
     * Match all request paths except forhe ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */ "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__c2258e89._.js.map