{"node": {"004cc80f306f3db1d62a7949054f8cad8e605e0bfd": {"workers": {"app/(auth)/choose-role/page": {"moduleId": "[project]/.next-internal/server/app/(auth)/choose-role/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(auth)/choose-role/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(auth)/choose-role/page": "action-browser"}}, "7023dd6215270a75db67ac1d595e9ec8f493978c41": {"workers": {"app/(auth)/choose-role/page": {"moduleId": "[project]/.next-internal/server/app/(auth)/choose-role/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/app/(auth)/choose-role/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(auth)/choose-role/page": "action-browser"}}}, "edge": {}}