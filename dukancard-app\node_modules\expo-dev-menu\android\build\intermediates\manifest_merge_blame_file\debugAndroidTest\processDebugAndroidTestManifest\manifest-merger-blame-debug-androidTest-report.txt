1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="expo.modules.devmenu.test" >
4
5    <uses-sdk
5-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:5:5-74
6        android:minSdkVersion="24"
6-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:5:15-41
7        android:targetSdkVersion="35" />
7-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:5:42-71
8
9    <instrumentation
9-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:11:5-15:75
10        android:name="android.test.InstrumentationTestRunner"
10-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:11:22-75
11        android:functionalTest="false"
11-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:14:22-52
12        android:handleProfiling="false"
12-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:13:22-53
13        android:label="Tests for expo.modules.devmenu.test"
13-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:15:22-73
14        android:targetPackage="expo.modules.devmenu.test" />
14-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:12:22-71
15    <!--
16    This manifest file is used only by Gradle to configure debug-only capabilities
17    for React Native Apps.
18    -->
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:5-78
19-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:16:22-75
20
21    <permission
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
22        android:name="expo.modules.devmenu.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="expo.modules.devmenu.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:7:5-9:19
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:debuggable="true"
30        android:extractNativeLibs="false" >
31        <uses-library android:name="android.test.runner" />
31-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:8:9-60
31-->C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest7586153583520932021.xml:8:23-57
32
33        <activity
33-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
34            android:name="expo.modules.devmenu.DevMenuActivity"
34-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
35            android:exported="true"
35-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
36            android:launchMode="singleTask"
36-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
37            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
37-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
38            <intent-filter>
38-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
39                <action android:name="android.intent.action.VIEW" />
39-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-69
39-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:25-66
40
41                <category android:name="android.intent.category.DEFAULT" />
41-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-76
41-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:27-73
42                <category android:name="android.intent.category.BROWSABLE" />
42-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:17-78
42-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:27-75
43
44                <data android:scheme="expo-dev-menu" />
44-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-56
44-->[:expo-dev-menu] C:\web-app\dukancard-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:23-53
45            </intent-filter>
46        </activity>
47
48        <meta-data
48-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
49            android:name="org.unimodules.core.AppLoader#react-native-headless"
49-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
50            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
50-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
51        <meta-data
51-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
52            android:name="com.facebook.soloader.enabled"
52-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
53            android:value="true" />
53-->[:expo-modules-core] C:\web-app\dukancard-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
54
55        <activity
55-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
56            android:name="com.facebook.react.devsupport.DevSettingsActivity"
56-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
57            android:exported="false" />
57-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\804afae0b0d5554ac4e7feda2f2ca6c6\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
58
59        <provider
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
60            android:name="androidx.startup.InitializationProvider"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
61            android:authorities="expo.modules.devmenu.test.androidx-startup"
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
62            android:exported="false" >
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
63            <meta-data
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.emoji2.text.EmojiCompatInitializer"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
65                android:value="androidx.startup" />
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
67-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
68                android:value="androidx.startup" />
68-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
71                android:value="androidx.startup" />
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
72        </provider>
73
74        <receiver
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
75            android:name="androidx.profileinstaller.ProfileInstallReceiver"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
76            android:directBootAware="false"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
77            android:enabled="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
78            android:exported="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
79            android:permission="android.permission.DUMP" >
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
81                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
84                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
87                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
90                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
91            </intent-filter>
92        </receiver>
93    </application>
94
95</manifest>
