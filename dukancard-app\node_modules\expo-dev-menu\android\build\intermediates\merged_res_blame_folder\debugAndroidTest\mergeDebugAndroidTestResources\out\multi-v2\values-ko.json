{"logs": [{"outputFile": "expo.modules.devmenu.test.expo-dev-menu-mergeDebugAndroidTestResources-35:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "38,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3500,3586,3679,3782", "endColumns": "85,92,102,93", "endOffsets": "3581,3674,3777,3871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,4652", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,4726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "30,31,32,33,34,35,36,62", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2751,2843,2943,3037,3134,3230,3328,5324", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "2838,2938,3032,3129,3225,3323,3423,5420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\804afae0b0d5554ac4e7feda2f2ca6c6\\transformed\\react-android-0.79.5-debug\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,327,404,469,534,606,678,755,830,896,969,1043,1116,1193,1269,1341,1411,1480,1562,1630,1701,1768", "endColumns": "65,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "116,188,255,322,399,464,529,601,673,750,825,891,964,1038,1111,1188,1264,1336,1406,1475,1557,1625,1696,1763,1835"}, "to": {"startLines": "29,37,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2685,3428,3876,3943,4010,4087,4152,4217,4289,4361,4438,4513,4579,4731,4805,4878,4955,5031,5103,5173,5242,5425,5493,5564,5631", "endColumns": "65,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "2746,3495,3938,4005,4082,4147,4212,4284,4356,4433,4508,4574,4647,4800,4873,4950,5026,5098,5168,5237,5319,5488,5559,5626,5698"}}]}]}