{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28auth%29/choose-role/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\nexport async function createCustomerProfile(\r\n  userId: string,\r\n  redirectSlug: string | null = null,\r\n  _message: string | null = null\r\n) {\r\n  if (!userId) {\r\n    return { error: \"User ID is required.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Fetch user details first\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    // Error fetching user in action\r\n    return { error: \"User not found or authentication error.\" };\r\n  }\r\n\r\n  // Check if customer profile already exists (extra safety check)\r\n  const { data: existingProfile, error: checkError } = await supabase\r\n    .from(\"customer_profiles\") // Use new table name\r\n    .select(\"id\")\r\n    .eq(\"id\", userId)\r\n    .maybeSingle();\r\n\r\n  if (checkError) {\r\n    // Error checking existing profile\r\n    return { error: \"Database error checking profile.\" };\r\n  }\r\n\r\n  if (existingProfile) {\r\n    // Profile already exists for user\r\n    // Redirect to the card page if redirectSlug is provided, otherwise to customer dashboard\r\n    if (redirectSlug) {\r\n      // Don't pass message parameter back to public card page\r\n      redirect(`/${redirectSlug}`);\r\n    } else {\r\n      // Redirect to customer dashboard anyway, assuming it's the correct type\r\n      redirect(\"/dashboard/customer\");\r\n    }\r\n  }\r\n\r\n  // Create the customer profile in the correct table\r\n  const { error: insertError } = await supabase\r\n    .from(\"customer_profiles\")\r\n    .insert({\r\n      // Use new table name\r\n      id: userId, // Should match user.id\r\n      // user_type is implicit by the table name now\r\n      // Populate name and email from auth user data\r\n      name: user.user_metadata?.full_name ?? user.user_metadata?.name ?? null, // Use full_name or name from metadata if available\r\n      email: user.email ?? null, // Use user's primary email\r\n    });\r\n\r\n  if (insertError) {\r\n    console.error(\"Error creating customer profile:\", insertError);\r\n    // Handle specific errors like unique constraint violation if necessary\r\n    return { error: \"Failed to create profile.\" };\r\n  }\r\n\r\n  // Revalidate relevant paths if needed, though middleware handles redirection mostly\r\n  revalidatePath(\"/choose-role\"); // Revalidate the current path\r\n  revalidatePath(\"/dashboard/customer\"); // Revalidate the target path\r\n\r\n  // Redirect to the card page if redirectSlug is provided, otherwise to customer dashboard\r\n  if (redirectSlug) {\r\n    // Don't pass message parameter back to public card page\r\n    redirect(`/${redirectSlug}`);\r\n  } else {\r\n    // Redirect to the customer dashboard after successful creation\r\n    redirect(\"/dashboard/customer\");\r\n  }\r\n\r\n  // Note: Redirect should happen before returning, but for clarity:\r\n  // return { success: true };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;;;;;;;AAEO,eAAe,sBACpB,MAAc,EACd,eAA8B,IAAI,EAClC,WAA0B,IAAI;IAE9B,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,OAAO;QAAuB;IACzC;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,2BAA2B;IAC3B,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,gCAAgC;QAChC,OAAO;YAAE,OAAO;QAA0C;IAC5D;IAEA,gEAAgE;IAChE,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,qBAAqB,qBAAqB;KAC/C,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,WAAW;IAEd,IAAI,YAAY;QACd,kCAAkC;QAClC,OAAO;YAAE,OAAO;QAAmC;IACrD;IAEA,IAAI,iBAAiB;QACnB,kCAAkC;QAClC,yFAAyF;QACzF,IAAI,cAAc;YAChB,wDAAwD;YACxD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,EAAE,cAAc;QAC7B,OAAO;YACL,wEAAwE;YACxE,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;QACX;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;QACN,qBAAqB;QACrB,IAAI;QACJ,8CAA8C;QAC9C,8CAA8C;QAC9C,MAAM,KAAK,aAAa,EAAE,aAAa,KAAK,aAAa,EAAE,QAAQ;QACnE,OAAO,KAAK,KAAK,IAAI;IACvB;IAEF,IAAI,aAAa;QACf,QAAQ,KAAK,CAAC,oCAAoC;QAClD,uEAAuE;QACvE,OAAO;YAAE,OAAO;QAA4B;IAC9C;IAEA,oFAAoF;IACpF,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,8BAA8B;IAC9D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,6BAA6B;IAEpE,yFAAyF;IACzF,IAAI,cAAc;QAChB,wDAAwD;QACxD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,EAAE,cAAc;IAC7B,OAAO;QACL,+DAA+D;QAC/D,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;AAEA,kEAAkE;AAClE,4BAA4B;AAC9B;;;IA/EsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28auth%29/choose-role/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '004cc80f306f3db1d62a7949054f8cad8e605e0bfd'} from 'ACTIONS_MODULE0'\nexport {createCustomerProfile as '7023dd6215270a75db67ac1d595e9ec8f493978c41'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28auth%29/choose-role/ChooseRoleClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(auth)/choose-role/ChooseRoleClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(auth)/choose-role/ChooseRoleClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28auth%29/choose-role/ChooseRoleClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(auth)/choose-role/ChooseRoleClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(auth)/choose-role/ChooseRoleClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28auth%29/choose-role/page.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { <PERSON>ada<PERSON> } from \"next\";\r\nimport ChooseRoleClient from \"./ChooseRoleClient\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect as nextRedirect } from \"next/navigation\";\r\n\r\nexport async function generateMetadata(): Promise<Metadata> {\r\n  return {\r\n    title: \"Choose Your Role\", // Uses template: \"Choose Your Role - Dukancard\"\r\n    description: \"Select how you will use Dukancard.\",\r\n    robots: \"noindex, nofollow\", // Keep preventing indexing and following\r\n  };\r\n}\r\n\r\n// This page should only be accessible to logged-in users without a profile\r\nexport default async function ChooseRolePage({\r\n  searchParams,\r\n}: {\r\n  searchParams: Promise<{ redirect?: string; message?: string }>;\r\n}) {\r\n  const supabase = await createClient();\r\n  const { redirect, message } = await searchParams;\r\n  const redirectSlug = redirect || null;\r\n  const messageParam = message || null;\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    // Should be handled by middleware, but good safeguard\r\n    return nextRedirect(\"/login\");\r\n  }\r\n\r\n  // Check if profile already exists in either table (middleware should prevent this, but double-check)\r\n  const [customerProfileRes, businessProfileRes] = await Promise.all([\r\n    supabase\r\n      .from(\"customer_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle(),\r\n    supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle(),\r\n  ]);\r\n\r\n  if (customerProfileRes.error || businessProfileRes.error) {\r\n    // Handle error appropriately - redirect to login for safety\r\n    return nextRedirect(\"/login?message=Error checking profile status\");\r\n  }\r\n\r\n  if (customerProfileRes.data || businessProfileRes.data) {\r\n    // User already has a profile, redirect them away\r\n    const userType = customerProfileRes.data ? \"customer\" : \"business\";\r\n    const redirectPath =\r\n      userType === \"business\" ? \"/dashboard/business\" : \"/dashboard/customer\";\r\n    return nextRedirect(redirectPath);\r\n  }\r\n\r\n  // If no profile exists and user is logged in, render the choice component\r\n  // This div acts as a minimal layout for this specific route\r\n  return (\r\n      <ChooseRoleClient userId={user.id} redirectSlug={redirectSlug} message={messageParam} />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;;;;;AAEO,eAAe;IACpB,OAAO;QACL,OAAO;QACP,aAAa;QACb,QAAQ;IACV;AACF;AAGe,eAAe,eAAe,EAC3C,YAAY,EAGb;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM;IACpC,MAAM,eAAe,YAAY;IACjC,MAAM,eAAe,WAAW;IAEhC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,sDAAsD;QACtD,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAY,AAAD,EAAE;IACtB;IAEA,qGAAqG;IACrG,MAAM,CAAC,oBAAoB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;QACjE,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QACd,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;KACf;IAED,IAAI,mBAAmB,KAAK,IAAI,mBAAmB,KAAK,EAAE;QACxD,4DAA4D;QAC5D,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAY,AAAD,EAAE;IACtB;IAEA,IAAI,mBAAmB,IAAI,IAAI,mBAAmB,IAAI,EAAE;QACtD,iDAAiD;QACjD,MAAM,WAAW,mBAAmB,IAAI,GAAG,aAAa;QACxD,MAAM,eACJ,aAAa,aAAa,wBAAwB;QACpD,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAY,AAAD,EAAE;IACtB;IAEA,0EAA0E;IAC1E,4DAA4D;IAC5D,qBACI,8OAAC,sJAAA,CAAA,UAAgB;QAAC,QAAQ,KAAK,EAAE;QAAE,cAAc;QAAc,SAAS;;;;;;AAE9E", "debugId": null}}]}